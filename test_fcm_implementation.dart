import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/config/flavor_config.dart';
import 'lib/providers/auth_provider.dart';
import 'lib/services/firebase_messaging_service.dart';
import 'lib/config/firebase_config.dart';

void main() {
  runApp(TestFCMApp());
}

class TestFCMApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize flavor configuration for testing
    FlavorConfig(
      flavor: Flavor.koifish,
      name: 'Master Ko<PERSON>',
      appId: 'com.masterkoi.bid',
      appStoreId: '**********',
      appName: 'Master Koi',
      baseUrl: 'https://masterkoi.bid',
      privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
      appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id**********',
      logoPath: 'assets/images/logo_koifish.png',
      primaryColor: '#11254E',
      accentColor: '#66BB6A',
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Test FCM Implementation',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: TestFCMScreen(),
      ),
    );
  }
}

class TestFCMScreen extends StatefulWidget {
  @override
  _TestFCMScreenState createState() => _TestFCMScreenState();
}

class _TestFCMScreenState extends State<TestFCMScreen> {
  String _fcmToken = 'Not initialized';
  String _status = 'Ready';

  @override
  void initState() {
    super.initState();
    _initializeFirebase();
  }

  Future<void> _initializeFirebase() async {
    try {
      setState(() {
        _status = 'Initializing Firebase...';
      });

      // Initialize Firebase
      await FirebaseConfig.initializeFirebase();

      setState(() {
        _status = 'Initializing FCM...';
      });

      // Initialize FCM
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await FirebaseMessagingService().initialize(authProvider);

      // Get current token
      final token = await FirebaseMessagingService().getCurrentToken();
      
      setState(() {
        _fcmToken = token ?? 'Failed to get token';
        _status = 'FCM initialized successfully';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _testSaveFCMToken() async {
    try {
      setState(() {
        _status = 'Testing FCM token save...';
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Simulate having a session (normally set during login)
      // In real app, this would be set during login process
      
      final response = await authProvider.post('/api/profil/set', {
        'token_fcm': _fcmToken,
      });

      final meta = response['meta'];
      final code = meta['code'] ?? 0;
      final message = meta['message'] ?? 'Unknown response';

      setState(() {
        _status = 'Response: Code $code - $message';
      });
    } catch (e) {
      setState(() {
        _status = 'Error testing FCM save: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test FCM Implementation'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'FCM Implementation Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            
            Text(
              'Status:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(_status),
            SizedBox(height: 20),
            
            Text(
              'FCM Token:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _fcmToken,
                style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ),
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _testSaveFCMToken,
              child: Text('Test Save FCM Token'),
            ),
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _initializeFirebase,
              child: Text('Reinitialize FCM'),
            ),
            SizedBox(height: 20),
            
            Text(
              'Implementation Details:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text('• Uses /api/profil/set endpoint (same as Swift)'),
            Text('• Parameter: token_fcm (same as Swift)'),
            Text('• Header: Session (same as Swift)'),
            Text('• Success codes: 200 or 406 (same as Swift)'),
            Text('• Auto-saves token when received'),
            Text('• Handles session expiration'),
          ],
        ),
      ),
    );
  }
}
