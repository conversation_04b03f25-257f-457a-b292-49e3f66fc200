name: masterkoi_app
description: "Master Koi App Flutter Version"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Network and API
  http: ^1.1.0
  dio: ^5.4.1
  retrofit: ^4.1.0
  json_annotation: ^4.8.1
  retrofit_generator: ^8.1.0

  # State Management
  provider: ^6.1.5
  flutter_bloc: ^8.1.4

  # UI and WebView
  webview_flutter: ^4.7.0

  # Firebase
  firebase_core: ^2.27.2
  firebase_auth: ^4.18.0
  firebase_database: ^10.4.10
  firebase_messaging: ^14.8.13
  firebase_analytics: ^10.8.12
  firebase_storage: ^11.6.13

  # UI Components
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  photo_view: ^0.15.0
  flutter_rating_bar: ^4.0.1
  pull_to_refresh: ^2.0.0
  font_awesome_flutter: ^10.7.0
  flutter_html: ^3.0.0-beta.2

  # Video Player
  video_player: ^2.8.3
  chewie: ^1.7.5

  # Utils
  intl: ^0.19.0
  url_launcher: ^6.2.1
  shared_preferences: ^2.2.2
  path_provider: ^2.1.2
  permission_handler: ^11.3.0
  share_plus: ^7.0.0
  # flutter_local_notifications: ^9.9.1 # Temporarily disabled due to namespace issues
  # qr_code_scanner: ^1.0.1 # Temporarily disabled due to namespace issues
  qr_flutter: ^4.1.0

  # Maps
  google_maps_flutter: ^2.6.0
  geolocator: ^11.0.0

  # Charts
  fl_chart: ^0.66.2

  # App Info and URLs
  package_info_plus: ^8.3.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  uuid: ^4.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  freezed: ^2.4.7
  freezed_annotation: ^2.4.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/arwanafish/
    - assets/images/goldfish/
    - assets/images/koifish/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
