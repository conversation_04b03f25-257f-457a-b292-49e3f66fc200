// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:masterkoi_app/config/firebase_config.dart';
import 'package:masterkoi_app/config/flavor_config.dart';

import 'package:masterkoi_app/main.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize flavor configuration
    FlavorConfig(
      flavor: Flavor.koifish,
      name: 'Master Koi Bot',
      appId: 'com.masterkoi.bid',
      appStoreId: '6450153338', // Add missing required parameter
      appName: 'Master Koi',
      baseUrl: 'https://masterkoi.bid',
      privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
      appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id6450153338',
      logoPath: 'assets/images/logo_koifish.png',
      primaryColor: '#11254E', // Biru tua
      accentColor: '#66BB6A', // Hijau
    );

    // Initialize Firebase
    await FirebaseConfig.initializeFirebase();

    // Get stored preferences
    final prefs = await SharedPreferences.getInstance();
    final String languageCode = prefs.getString('language_code') ?? '';
    final bool hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

    // Build our app and trigger a frame.
    await tester.pumpWidget(MasterKoiApp(
      initialLocale: Locale(languageCode),
      hasSeenOnboarding: hasSeenOnboarding,
    ));

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
