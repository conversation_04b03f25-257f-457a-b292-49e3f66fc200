import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/config/flavor_config.dart';
import 'lib/providers/auth_provider.dart';
import 'lib/screens/auth/phone_input_screen.dart';

void main() {
  runApp(TestLayoutApp());
}

class TestLayoutApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize flavor configuration for testing
    FlavorConfig(
      flavor: Flavor.koifish,
      name: 'Master Ko<PERSON> Bo<PERSON>',
      appId: 'com.masterkoi.bid',
      appStoreId: '**********',
      appName: 'Master Koi',
      baseUrl: 'https://masterkoi.bid',
      privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
      appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id**********',
      logoPath: 'assets/images/logo_koifish.png',
      primaryColor: '#11254E',
      accentColor: '#66BB6A',
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Test Layout Fix',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: TestLayoutScreen(),
      ),
    );
  }
}

class TestLayoutScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Layout Fix'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Layout Fix Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            
            Text(
              'The phone input screen layout has been fixed to resolve the RenderFlex unbounded height constraint error.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 20),
            
            Text(
              'Changes made:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            
            Text('• Wrapped Column in LayoutBuilder'),
            Text('• Added ConstrainedBox with minHeight'),
            Text('• Used IntrinsicHeight for proper sizing'),
            Text('• Replaced Spacer() with SizedBox()'),
            SizedBox(height: 30),
            
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PhoneInputScreen(
                      onSuccess: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Layout test successful!')),
                        );
                        Navigator.pop(context);
                      },
                    ),
                  ),
                );
              },
              child: Text('Test Phone Input Screen'),
            ),
            SizedBox(height: 20),
            
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border.all(color: Colors.green),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'Layout Issue Fixed',
                    style: TextStyle(
                      color: Colors.green.shade800,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'No more RenderFlex unbounded height errors',
                    style: TextStyle(color: Colors.green.shade700),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
