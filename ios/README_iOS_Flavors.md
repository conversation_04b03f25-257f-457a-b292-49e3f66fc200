# iOS Flavor Configuration Guide

This document explains how to configure and build iOS apps for different flavors in the Master Koi Flutter project.

## Overview

The iOS project supports 3 flavors:
- **koifish** - Master Koi (com.masterkoi.bid)
- **goldfish** - Gold Fish (com.goldfishop.bid)  
- **arwanafish** - <PERSON>rwana Fish (com.aronesia.bid)

## Project Structure

```
ios/
├── Firebase/                          # Firebase configurations
│   ├── koifish/GoogleService-Info.plist
│   ├── goldfish/GoogleService-Info.plist
│   └── arwanafish/GoogleService-Info.plist
├── Flutter/                           # Flutter build configurations
│   ├── Debug-koifish.xcconfig
│   ├── Release-koifish.xcconfig
│   ├── Debug-goldfish.xcconfig
│   ├── Release-goldfish.xcconfig
│   ├── Debug-arwanafish.xcconfig
│   └── Release-arwanafish.xcconfig
├── Runner/                            # iOS app source
│   ├── Info-koifish.plist
│   ├── Info-goldfish.plist
│   └── Info-arwanafish.plist
├── Runner.xcodeproj/xcshareddata/xcschemes/  # Xcode schemes
│   ├── Runner-koifish.xcscheme
│   ├── Runner-goldfish.xcscheme
│   └── Runner-arwanafish.xcscheme
└── scripts/                           # Build scripts
    ├── setup_flavor.sh
    └── build_ios.sh
```

## Flavor Configuration Details

### KoiFish (Master Koi)
- **Bundle ID**: com.masterkoi.bid
- **Display Name**: Master Koi
- **Firebase Project**: koi-show
- **Version**: 3.8.1 (181)
- **URL Schemes**: masterkoi, com.masterkoi.bid
- **Universal Links**: masterkoi.bid

### GoldFish
- **Bundle ID**: com.goldfishop.bid
- **Display Name**: Gold Fish
- **Firebase Project**: goldfishbid
- **Version**: 0.1.7 (7)
- **URL Schemes**: goldfish, com.goldfishop.bid
- **Universal Links**: goldfishop.bid

### ArwanaFish
- **Bundle ID**: com.aronesia.bid
- **Display Name**: Arwana Fish
- **Firebase Project**: aronesia-5688e
- **Version**: 1.0.3 (3)
- **URL Schemes**: arwanafish, com.aronesia.bid
- **Universal Links**: aronesia.bid

## Setup Instructions

### 1. Firebase Configuration

For each flavor, you need to add the corresponding `GoogleService-Info.plist` file:

1. Download the file from Firebase Console for each project
2. Place them in the appropriate directories:
   - `ios/Firebase/koifish/GoogleService-Info.plist`
   - `ios/Firebase/goldfish/GoogleService-Info.plist`
   - `ios/Firebase/arwanafish/GoogleService-Info.plist`

### 2. Code Signing

Update the `DEVELOPMENT_TEAM` in the xcconfig files with your Apple Developer Team ID:
- `ios/Flutter/Debug-*.xcconfig`
- `ios/Flutter/Release-*.xcconfig`

### 3. App Icons

Create flavor-specific app icons in `ios/Runner/Assets.xcassets/`:
- `AppIcon-koifish.appiconset`
- `AppIcon-goldfish.appiconset`
- `AppIcon-arwanafish.appiconset`

## Building iOS Apps

### Using Scripts (Recommended)

```bash
# Setup flavor configuration
./ios/scripts/setup_flavor.sh koifish

# Build debug version
./ios/scripts/build_ios.sh koifish debug

# Build release version
./ios/scripts/build_ios.sh koifish release
```

### Using Flutter Commands

```bash
# Debug builds
flutter run --flavor koifish -t lib/main_koifish.dart
flutter run --flavor goldfish -t lib/main_goldfish.dart
flutter run --flavor arwanafish -t lib/main_arwanafish.dart

# Release builds
flutter build ios --release --flavor koifish -t lib/main_koifish.dart
flutter build ios --release --flavor goldfish -t lib/main_goldfish.dart
flutter build ios --release --flavor arwanafish -t lib/main_arwanafish.dart

# Build IPA for distribution
flutter build ipa --release --flavor koifish -t lib/main_koifish.dart
```

### Using Xcode

1. Open `ios/Runner.xcworkspace` in Xcode
2. Select the appropriate scheme:
   - Runner-koifish
   - Runner-goldfish
   - Runner-arwanafish
3. Choose your target device/simulator
4. Build and run (⌘+R)

## Deep Linking Configuration

Each flavor supports both custom URL schemes and Universal Links:

### Custom URL Schemes
- koifish: `masterkoi://` and `com.masterkoi.bid://`
- goldfish: `goldfish://` and `com.goldfishop.bid://`
- arwanafish: `arwanafish://` and `com.aronesia.bid://`

### Universal Links
- koifish: `https://masterkoi.bid/*`
- goldfish: `https://goldfishop.bid/*`
- arwanafish: `https://aronesia.bid/*`

## Permissions

Each flavor includes the following permissions:
- Camera access (for fish photos)
- Photo library access (for selecting images)
- Location access (for nearby auctions)
- Push notifications
- WhatsApp URL scheme queries

## Troubleshooting

### Common Issues

1. **Firebase Configuration Not Found**
   - Ensure `GoogleService-Info.plist` files are in the correct directories
   - Run the setup script: `./ios/scripts/setup_flavor.sh <flavor>`

2. **Code Signing Issues**
   - Update `DEVELOPMENT_TEAM` in xcconfig files
   - Ensure you have valid certificates in Xcode

3. **Scheme Not Found**
   - Ensure scheme files are in `ios/Runner.xcodeproj/xcshareddata/xcschemes/`
   - Refresh Xcode project

4. **Build Errors**
   - Clean build: `flutter clean && flutter pub get`
   - Delete derived data in Xcode
   - Restart Xcode

### Logs and Debugging

```bash
# Enable verbose logging
flutter run --verbose --flavor koifish -t lib/main_koifish.dart

# Check iOS logs
flutter logs

# Xcode console logs
# Open Xcode → Window → Devices and Simulators → Select device → Open Console
```

## Distribution

### App Store Connect

Each flavor needs to be configured as a separate app in App Store Connect:

1. Create separate app records for each bundle ID
2. Configure app metadata for each flavor
3. Upload builds using Xcode or Application Loader
4. Submit for review

### TestFlight

```bash
# Build and upload to TestFlight
flutter build ipa --release --flavor koifish -t lib/main_koifish.dart
# Upload the generated IPA using Xcode or Transporter app
```

## Next Steps

1. **Add Real Firebase Configurations**: Replace placeholder Firebase files with actual configurations
2. **Create App Icons**: Design and add flavor-specific app icons
3. **Configure Code Signing**: Set up proper certificates and provisioning profiles
4. **Test Deep Linking**: Verify URL schemes and Universal Links work correctly
5. **Setup CI/CD**: Automate builds using GitHub Actions or similar

## Support

For issues related to iOS flavor configuration, check:
1. This documentation
2. Flutter iOS documentation
3. Xcode documentation
4. Firebase iOS setup guide
