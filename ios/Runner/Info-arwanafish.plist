<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>A<PERSON><PERSON> Fish</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Ar<PERSON> Fish</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- URL Schemes for Deep Linking -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.aronesia.bid</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>arwanafish</string>
				<string>com.aronesia.bid</string>
			</array>
		</dict>
	</array>
	
	<!-- Associated Domains for Universal Links -->
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:aronesia.bid</string>
		<string>applinks:www.aronesia.bid</string>
	</array>
	
	<!-- Firebase Configuration -->
	<key>FIREBASE_ANALYTICS_COLLECTION_ENABLED</key>
	<true/>
	<key>FIREBASE_CRASHLYTICS_COLLECTION_ENABLED</key>
	<true/>
	
	<!-- WhatsApp URL Scheme -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>whatsapp</string>
		<string>telprompt</string>
		<string>tel</string>
	</array>
	
	<!-- Camera and Photo Library Permissions -->
	<key>NSCameraUsageDescription</key>
	<string>Arwana Fish needs camera access to take photos of fish</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Arwana Fish needs photo library access to select fish images</string>
	
	<!-- Location Permission -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Arwana Fish needs location access to show nearby fish auctions</string>
	
	<!-- Push Notifications -->
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>background-fetch</string>
	</array>
</dict>
</plist>
