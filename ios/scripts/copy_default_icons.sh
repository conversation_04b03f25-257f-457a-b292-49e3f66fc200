#!/bin/bash

# Copy Default Icons Script
# This script copies the existing default icons to all flavor directories
# Use this as a starting point, then replace with flavor-specific icons

set -e

FLAVOR=$1
if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor>"
    echo "Available flavors: koifish, goldfish, arwanafish, all"
    exit 1
fi

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
SOURCE_DIR="$PROJECT_ROOT/ios/Runner/Assets.xcassets/AppIcon.appiconset"

copy_icons_for_flavor() {
    local flavor=$1
    local target_dir="$PROJECT_ROOT/ios/Runner/Assets.xcassets/AppIcon-$flavor.appiconset"
    
    echo "Copying default icons for flavor: $flavor"
    echo "Target directory: $target_dir"
    
    # Copy all icon files
    cp "$SOURCE_DIR"/*.png "$target_dir/" 2>/dev/null || echo "No PNG files to copy"
    
    echo "✅ Icons copied for $flavor"
}

# Copy icons based on flavor parameter
case $FLAVOR in
    koifish)
        copy_icons_for_flavor "koifish"
        ;;
    goldfish)
        copy_icons_for_flavor "goldfish"
        ;;
    arwanafish)
        copy_icons_for_flavor "arwanafish"
        ;;
    all)
        copy_icons_for_flavor "koifish"
        copy_icons_for_flavor "goldfish"
        copy_icons_for_flavor "arwanafish"
        ;;
    *)
        echo "Invalid flavor: $FLAVOR"
        echo "Available flavors: koifish, goldfish, arwanafish, all"
        exit 1
        ;;
esac

echo ""
echo "✅ Default icons copied successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Replace the copied icons with flavor-specific designs"
echo "2. Use design software or online tools to create proper iOS icons"
echo "3. Ensure icons follow iOS Human Interface Guidelines"
echo ""
echo "📐 Required icon sizes:"
echo "- 20x20 (@1x, @2x, @3x)"
echo "- 29x29 (@1x, @2x, @3x)"
echo "- 40x40 (@1x, @2x, @3x)"
echo "- 60x60 (@2x, @3x)"
echo "- 76x76 (@1x, @2x)"
echo "- 83.5x83.5 (@2x)"
echo "- 1024x1024 (@1x) - App Store"
echo ""
echo "🎨 Design guidelines:"
echo "- Use your flavor logos: assets/images/logo_koifish.png, etc."
echo "- Icons should be square with rounded corners (iOS adds them automatically)"
echo "- Use high contrast and clear imagery"
echo "- Test on different backgrounds"
echo ""
echo "🔧 Tools you can use:"
echo "- App Icon Generator online tools"
echo "- Sketch, Figma, or Adobe Illustrator"
echo "- ImageMagick (if installed): ./ios/scripts/generate_app_icons.sh <flavor>"
