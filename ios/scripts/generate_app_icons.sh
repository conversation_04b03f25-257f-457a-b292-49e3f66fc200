#!/bin/bash

# iOS App Icon Generator Script
# This script generates iOS app icons from flavor logos

set -e

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "ImageMagick is required but not installed."
    echo "Install it using: brew install imagemagick"
    echo ""
    echo "Alternative: Use online tools or design software to create icons manually"
    echo "Required sizes: 20, 29, 40, 60, 76, 83.5, 1024 (with @1x, @2x, @3x variants)"
    exit 1
fi

FLAVOR=$1
if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor>"
    echo "Available flavors: koifish, goldfish, arwanafish"
    exit 1
fi

# Validate flavor
case $FLAVOR in
    koifish|goldfish|arwanafish)
        echo "Generating iOS app icons for flavor: $FLAVOR"
        ;;
    *)
        echo "Invalid flavor: $FLAVOR"
        echo "Available flavors: koifish, goldfish, arwanafish"
        exit 1
        ;;
esac

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
ASSETS_DIR="$PROJECT_ROOT/assets/images"
OUTPUT_DIR="$PROJECT_ROOT/ios/Runner/Assets.xcassets/AppIcon-$FLAVOR.appiconset"

# Source logo file
SOURCE_LOGO="$ASSETS_DIR/logo_$FLAVOR.png"

if [ ! -f "$SOURCE_LOGO" ]; then
    echo "Error: Source logo not found: $SOURCE_LOGO"
    exit 1
fi

echo "Source logo: $SOURCE_LOGO"
echo "Output directory: $OUTPUT_DIR"

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Function to generate icon with specific size
generate_icon() {
    local size=$1
    local scale=$2
    local filename=$3
    
    local pixel_size=$((size * scale))
    local output_file="$OUTPUT_DIR/$filename"
    
    echo "Generating $filename (${pixel_size}x${pixel_size})"
    
    # Generate icon with rounded corners and proper iOS styling
    convert "$SOURCE_LOGO" \
        -resize "${pixel_size}x${pixel_size}" \
        -background transparent \
        -gravity center \
        -extent "${pixel_size}x${pixel_size}" \
        \( +clone -alpha extract \
           -draw "fill black polygon 0,0 0,15 15,0 fill white circle 15,15 15,0" \
           \( +clone -flip \) -compose Multiply -composite \
           \( +clone -flop \) -compose Multiply -composite \
        \) -alpha off -compose CopyOpacity -composite \
        "$output_file"
}

# Generate all required icon sizes
echo "Generating iOS app icons..."

# iPhone icons
generate_icon 20 2 "<EMAIL>"
generate_icon 20 3 "<EMAIL>"
generate_icon 29 1 "<EMAIL>"
generate_icon 29 2 "<EMAIL>"
generate_icon 29 3 "<EMAIL>"
generate_icon 40 2 "<EMAIL>"
generate_icon 40 3 "<EMAIL>"
generate_icon 60 2 "<EMAIL>"
generate_icon 60 3 "<EMAIL>"

# iPad icons
generate_icon 20 1 "<EMAIL>"
generate_icon 40 1 "<EMAIL>"
generate_icon 76 1 "<EMAIL>"
generate_icon 76 2 "<EMAIL>"
generate_icon 83 2 "<EMAIL>"  # 83.5 * 2 = 167

# App Store icon (no rounded corners for this one)
echo "Generating App Store icon (1024x1024)"
convert "$SOURCE_LOGO" \
    -resize "1024x1024" \
    -background transparent \
    -gravity center \
    -extent "1024x1024" \
    "$OUTPUT_DIR/<EMAIL>"

echo "✅ iOS app icons generated successfully for $FLAVOR!"
echo ""
echo "Generated files in: $OUTPUT_DIR"
echo ""
echo "Next steps:"
echo "1. Review the generated icons"
echo "2. Run: ./ios/scripts/setup_flavor.sh $FLAVOR"
echo "3. Build the app: flutter build ios --flavor $FLAVOR -t lib/main_$FLAVOR.dart"
echo ""
echo "Note: You may want to manually optimize the icons using design software"
echo "for better visual quality and proper iOS design guidelines."
