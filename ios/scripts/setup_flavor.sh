#!/bin/bash

# iOS Flavor Setup Script
# This script configures the iOS project for different flavors

set -e

FLAVOR=$1
if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor>"
    echo "Available flavors: koifish, goldfish, arwanafish"
    exit 1
fi

# Validate flavor
case $FLAVOR in
    koifish|goldfish|arwanafish)
        echo "Setting up iOS project for flavor: $FLAVOR"
        ;;
    *)
        echo "Invalid flavor: $FLAVOR"
        echo "Available flavors: koifish, goldfish, arwanafish"
        exit 1
        ;;
esac

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
IOS_DIR="$PROJECT_ROOT/ios"

echo "Project root: $PROJECT_ROOT"
echo "iOS directory: $IOS_DIR"

# Copy flavor-specific Info.plist
echo "Copying Info-$FLAVOR.plist to Info.plist..."
cp "$IOS_DIR/Runner/Info-$FLAVOR.plist" "$IOS_DIR/Runner/Info.plist"

# Copy flavor-specific Firebase configuration
echo "Copying Firebase configuration for $FLAVOR..."
if [ -f "$IOS_DIR/Firebase/$FLAVOR/GoogleService-Info.plist" ]; then
    cp "$IOS_DIR/Firebase/$FLAVOR/GoogleService-Info.plist" "$IOS_DIR/Runner/GoogleService-Info.plist"
    echo "Firebase configuration copied successfully"
else
    echo "Warning: Firebase configuration not found for $FLAVOR"
    echo "Please add GoogleService-Info.plist to $IOS_DIR/Firebase/$FLAVOR/"
fi

# Update xcconfig files to point to the correct flavor
echo "Updating xcconfig files for $FLAVOR..."

# Update Debug.xcconfig
cat > "$IOS_DIR/Flutter/Debug.xcconfig" << EOF
#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"
#include "Generated.xcconfig"
#include "Debug-$FLAVOR.xcconfig"
EOF

# Update Release.xcconfig
cat > "$IOS_DIR/Flutter/Release.xcconfig" << EOF
#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"
#include "Generated.xcconfig"
#include "Release-$FLAVOR.xcconfig"
EOF

echo "iOS flavor setup completed for: $FLAVOR"
echo ""
echo "Next steps:"
echo "1. Open ios/Runner.xcworkspace in Xcode"
echo "2. Select the appropriate scheme for your flavor"
echo "3. Build and run the project"
echo ""
echo "To build from command line:"
echo "flutter build ios --flavor $FLAVOR -t lib/main_$FLAVOR.dart"
