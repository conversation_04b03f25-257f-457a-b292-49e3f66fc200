#!/bin/bash

# iOS Build Script for Multiple Flavors
# This script builds iOS apps for different flavors

set -e

FLAVOR=$1
BUILD_TYPE=${2:-debug}

if [ -z "$FLAVOR" ]; then
    echo "Usage: $0 <flavor> [build_type]"
    echo "Available flavors: koifish, goldfish, arwanafish"
    echo "Available build types: debug, release, profile"
    exit 1
fi

# Validate flavor
case $FLAVOR in
    koifish|goldfish|arwanafish)
        echo "Building iOS app for flavor: $FLAVOR ($BUILD_TYPE)"
        ;;
    *)
        echo "Invalid flavor: $FLAVOR"
        echo "Available flavors: koifish, goldfish, arwanafish"
        exit 1
        ;;
esac

# Validate build type
case $BUILD_TYPE in
    debug|release|profile)
        ;;
    *)
        echo "Invalid build type: $BUILD_TYPE"
        echo "Available build types: debug, release, profile"
        exit 1
        ;;
esac

# Project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
cd "$PROJECT_ROOT"

echo "Project root: $PROJECT_ROOT"

# Setup flavor configuration
echo "Setting up flavor configuration..."
./ios/scripts/setup_flavor.sh $FLAVOR

# Clean previous builds
echo "Cleaning previous builds..."
flutter clean
flutter pub get

# Build iOS app
echo "Building iOS app..."
case $BUILD_TYPE in
    debug)
        flutter build ios --debug --flavor $FLAVOR -t lib/main_$FLAVOR.dart
        ;;
    release)
        flutter build ios --release --flavor $FLAVOR -t lib/main_$FLAVOR.dart
        ;;
    profile)
        flutter build ios --profile --flavor $FLAVOR -t lib/main_$FLAVOR.dart
        ;;
esac

echo "iOS build completed successfully!"
echo ""
echo "Build artifacts location:"
echo "build/ios/iphoneos/Runner.app"
echo ""
echo "To run on simulator:"
echo "flutter run --flavor $FLAVOR -t lib/main_$FLAVOR.dart"
echo ""
echo "To build IPA for distribution:"
echo "flutter build ipa --release --flavor $FLAVOR -t lib/main_$FLAVOR.dart"
