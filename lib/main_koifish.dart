import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:masterkoi_app/config/firebase_config.dart';
import 'main.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorConfig(
    flavor: Flavor.koifish,
    name: 'Master Koi Bot',
    appId: 'com.masterkoi.bid',
    appStoreId : '6450153338',
    appName: 'Master Koi',
    baseUrl: 'https://masterkoi.bid',
    privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
    appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id6450153338',
    logoPath: 'assets/images/logo_koifish.png',
    primaryColor: '#11254E', // Biru tua
    accentColor: '#66BB6A', // Hijau
  );

  // Initialize Firebase
  await FirebaseConfig.initializeFirebase();

  // Get stored preferences
  final prefs = await SharedPreferences.getInstance();
  final String languageCode = prefs.getString('language_code') ?? 'en';
  final bool hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  // Run the app
  runApp(MasterKoiApp(
    initialLocale: Locale(languageCode),
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}
