import 'package:flutter/material.dart';

class InteractiveStarRating extends StatefulWidget {
  final double initialRating;
  final int maxRating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final Function(double) onRatingChanged;
  final bool allowHalfRating;
  final bool isEditable;

  const InteractiveStarRating({
    Key? key,
    this.initialRating = 0.0,
    this.maxRating = 5,
    this.size = 30.0,
    this.activeColor = Colors.amber,
    this.inactiveColor = Colors.grey,
    required this.onRatingChanged,
    this.allowHalfRating = true,
    this.isEditable = true,
  }) : super(key: key);

  @override
  State<InteractiveStarRating> createState() => _InteractiveStarRatingState();
}

class _InteractiveStarRatingState extends State<InteractiveStarRating> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        return GestureDetector(
          onTap: widget.isEditable ? () => _handleTap(index) : null,
          onPanUpdate: widget.isEditable ? (details) => _handlePanUpdate(details, index) : null,
          child: Icon(
            _getStarIcon(index),
            size: widget.size,
            color: _getStarColor(index),
          ),
        );
      }),
    );
  }

  IconData _getStarIcon(int index) {
    if (index < _currentRating.floor()) {
      return Icons.star;
    } else if (index == _currentRating.floor() && _currentRating % 1 != 0) {
      return Icons.star_half;
    }
    return Icons.star_border;
  }

  Color _getStarColor(int index) {
    if (index < _currentRating) {
      return widget.activeColor;
    }
    return widget.inactiveColor;
  }

  void _handleTap(int index) {
    setState(() {
      _currentRating = (index + 1).toDouble();
    });
    widget.onRatingChanged(_currentRating);
  }

  void _handlePanUpdate(DragUpdateDetails details, int index) {
    if (!widget.allowHalfRating) return;

    RenderBox box = context.findRenderObject() as RenderBox;
    Offset localPosition = box.globalToLocal(details.globalPosition);
    double starWidth = widget.size;
    double relativePosition = localPosition.dx / starWidth;
    
    double newRating;
    if (relativePosition < 0.5) {
      newRating = index + 0.5;
    } else {
      newRating = index + 1.0;
    }

    newRating = newRating.clamp(0.0, widget.maxRating.toDouble());

    if (newRating != _currentRating) {
      setState(() {
        _currentRating = newRating;
      });
      widget.onRatingChanged(_currentRating);
    }
  }
}

// Rating Display Widget (Read-only)
class RatingDisplay extends StatelessWidget {
  final double rating;
  final int maxRating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final bool showRatingText;
  final String? ratingText;

  const RatingDisplay({
    Key? key,
    required this.rating,
    this.maxRating = 5,
    this.size = 16.0,
    this.activeColor = Colors.amber,
    this.inactiveColor = Colors.grey,
    this.showRatingText = false,
    this.ratingText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(maxRating, (index) {
          return Icon(
            _getStarIcon(index),
            size: size,
            color: _getStarColor(index),
          );
        }),
        if (showRatingText) ...[
          const SizedBox(width: 4),
          Text(
            ratingText ?? rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.8,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStarIcon(int index) {
    if (index < rating.floor()) {
      return Icons.star;
    } else if (index == rating.floor() && rating % 1 != 0) {
      return Icons.star_half;
    }
    return Icons.star_border;
  }

  Color _getStarColor(int index) {
    if (index < rating) {
      return activeColor;
    }
    return inactiveColor;
  }
}

// Rating Summary Widget
class RatingSummary extends StatelessWidget {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;
  final double size;

  const RatingSummary({
    Key? key,
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    this.size = 16.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            RatingDisplay(
              rating: averageRating,
              size: size * 1.5,
              showRatingText: true,
            ),
            const SizedBox(width: 8),
            Text(
              '($totalReviews reviews)',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: size,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...List.generate(5, (index) {
          final starNumber = 5 - index;
          final count = ratingDistribution[starNumber] ?? 0;
          final percentage = totalReviews > 0 ? count / totalReviews : 0.0;
          
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text('$starNumber'),
                const SizedBox(width: 4),
                Icon(Icons.star, size: size, color: Colors.amber),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: percentage,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
                  ),
                ),
                const SizedBox(width: 8),
                Text('$count'),
              ],
            ),
          );
        }),
      ],
    );
  }
}
