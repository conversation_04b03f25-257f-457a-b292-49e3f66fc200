import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:provider/provider.dart';
import '../config/flavor_config.dart';
import '../models/reklame.dart';

class WarningMessageView extends StatelessWidget {
  const WarningMessageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.red,
      padding: const EdgeInsets.all(8),
      child: const Text(
        "Mengingat maraknya aksi penipuan saat ini. Master Koi BOT TIDAK BERTANGGUNGJAWAB & TIDAK MENERIMA KOMPLAIN atas transaksi yang TIDAK melalui Rekening Penjamin (Rekpen).",
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class ReklameLayoutView extends StatefulWidget {
  const ReklameLayoutView({super.key});

  @override
  State<ReklameLayoutView> createState() => _ReklameLayoutViewState();
}

class _ReklameLayoutViewState extends State<ReklameLayoutView> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    // Auto scroll every 4 seconds
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
        );
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (int page) {
          setState(() {
            _currentPage = page;
          });
        },
        itemBuilder: (context, index) {
          return Container(
            color: Colors.grey[200],
            child: const Center(
              child: Text("Reklame Banner"),
            ),
          );
        },
        itemCount: 3, // Replace with actual reklame count
      ),
    );
  }
}


class CardViewMenuWebview extends StatelessWidget {
  final IconData icon;
  final String text;
  final String group;
  final Function(String url) onTap;

  const CardViewMenuWebview({
    super.key,
    required this.icon,
    required this.text,
    required this.group,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final String url = "https://masterkoi.bid/$text".toLowerCase().replaceAll(' ', '-');
    return Card(
      child: InkWell(
        onTap: () => onTap(url),
        child: SizedBox(
          height: 80,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(height: 4),
              Text(
                text,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class WebViewScreen extends StatelessWidget {
  final String url;
  final Function(String fishId)? onFishDetailNavigate;

  const WebViewScreen({
    super.key,
    required this.url,
    this.onFishDetailNavigate, required String title,
  });

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(
      controller: WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..loadRequest(Uri.parse(url))
        ..setNavigationDelegate(
          NavigationDelegate(
            onNavigationRequest: (NavigationRequest request) {
              // Handle fish detail navigation
              if (request.url.contains('ikan/detail/')) {
                final fishId = request.url.split('/').last;
                onFishDetailNavigate?.call(fishId);
                return NavigationDecision.prevent;
              }
              return NavigationDecision.navigate;
            },
          ),
        ),
    );
  }
}

class PointHistoryScreen extends StatelessWidget {
  const PointHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Riwayat Poin"),
      ),
      body: ListView.builder(
        itemCount: 0, // Replace with actual point history count
        itemBuilder: (context, index) {
          return const ListTile(
            title: Text("Point Transaction"),
            subtitle: Text("Points: +100"),
            trailing: Text("2025-05-28"),
          );
        },
      ),
    );
  }
}
