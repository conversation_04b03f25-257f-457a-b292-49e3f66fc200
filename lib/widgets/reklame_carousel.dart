import 'package:flutter/material.dart';
import 'dart:async';

class ReklameItem {
  final String imageUrl;
  final String? link;

  ReklameItem({required this.imageUrl, this.link});
}

class ReklameCarousel extends StatefulWidget {
  const ReklameCarousel({Key? key}) : super(key: key);

  @override
  State<ReklameCarousel> createState() => _ReklameCarouselState();
}

class _ReklameCarouselState extends State<ReklameCarousel> {
  int _currentIndex = 0;
  List<ReklameItem> _reklameList = [];
  bool _isLoading = true;
  final PageController _pageController = PageController(viewportFraction: 0.8);
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _loadReklameData();
    _startAutoPlay();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoPlay() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_reklameList.isNotEmpty) {
        if (_currentIndex < _reklameList.length - 1) {
          _currentIndex++;
        } else {
          _currentIndex = 0;
        }
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 800),
          curve: Curves.fastOutSlowIn,
        );
      }
    });
  }

  Future<void> _loadReklameData() async {
    // TODO: Implement API call to load reklame data
    // For now, using dummy data
    _reklameList = [
      ReklameItem(
        imageUrl: 'assets/images/reklame1.jpg',
        link: 'https://example.com/reklame1',
      ),
      ReklameItem(
        imageUrl: 'assets/images/reklame2.jpg',
        link: 'https://example.com/reklame2',
      ),
    ];

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_reklameList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _pageController,
            itemCount: _reklameList.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final item = _reklameList[index];
              return GestureDetector(
                onTap: () {
                  if (item.link != null) {
                    // TODO: Implement link handling
                  }
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
                      image: AssetImage(item.imageUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: _reklameList.asMap().entries.map((entry) {
            return Container(
              width: 8.0,
              height: 8.0,
              margin: const EdgeInsets.symmetric(horizontal: 4.0),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blue.withOpacity(
                  _currentIndex == entry.key ? 0.9 : 0.4,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
