import 'package:flutter/material.dart';
import '../services/whatsapp_service.dart';

class WhatsAppButton extends StatefulWidget {
  final String? templateId;
  final Map<String, String> replacements;
  final IconData? icon;
  final String? buttonText;
  final Color? backgroundColor;
  final Color? textColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool showAdminSelection;
  final Function(bool)? onResult;
  final String? phoneNumber; // Added phone number parameter
  final String? message; // Added message parameter

  const WhatsAppButton({
    Key? key,
    this.templateId,
    this.replacements = const {},
    this.icon,
    this.buttonText,
    this.backgroundColor = Colors.green,
    this.textColor = Colors.white,
    this.borderRadius = 8.0,
    this.padding,
    this.showAdminSelection = false,
    this.onResult,
    this.phoneNumber,
    this.message,
  }) : super(key: key);

  @override
  State<WhatsAppButton> createState() => _WhatsAppButtonState();
}

class _WhatsAppButtonState extends State<WhatsAppButton> {
  final WhatsAppService _whatsAppService = WhatsAppService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleWhatsAppButtonPress,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.backgroundColor,
        foregroundColor: widget.textColor,
        padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.0),
        ),
      ),
      child: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.icon != null) ...[
                  Icon(widget.icon),
                  const SizedBox(width: 8),
                ],
                Text(widget.buttonText ?? 'Chat WhatsApp'),
              ],
            ),
    );
  }

  Future<void> _handleWhatsAppButtonPress() async {
    if (widget.phoneNumber == null || widget.message == null) {
      _showErrorSnackBar('Phone number and message are required');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _sendWhatsAppMessage(
        no: widget.phoneNumber!,
        message: widget.message!,
      );
    } catch (e) {
      _showErrorSnackBar('Terjadi kesalahan: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sendWhatsAppMessage({required String no, required String message}) async {
    // Dapatkan admin default jika tidak ada admin yang dipilih

    // Buka WhatsApp
    final success = await _whatsAppService.openWhatsApp(
      phoneNumber: no,
      message: message,
    );

    if (widget.onResult != null) {
      widget.onResult!(success);
    }

    if (!success && mounted) {
      _showErrorSnackBar('Tidak dapat membuka WhatsApp');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
