import 'package:flutter/material.dart';
import '../utils/colors.dart';

class SortDialog extends StatefulWidget {
  final String currentSort;
  final Function(String) onSortSelected;
  final VoidCallback? onReset;

  const SortDialog({
    Key? key,
    required this.currentSort,
    required this.onSortSelected,
    this.onReset,
  }) : super(key: key);

  @override
  State<SortDialog> createState() => _SortDialogState();
}

class _SortDialogState extends State<SortDialog> {
  late String selectedSort;

  @override
  void initState() {
    super.initState();
    selectedSort = widget.currentSort;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Urutkan',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedSort = '';
                        });
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        side: BorderSide(color: AppColors.primary, width: 1),
                      ),
                      child: Text(
                        'Clear',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        if (widget.onReset != null) {
                          widget.onReset!();
                        }
                        Navigator.pop(context);
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        side: BorderSide(color: AppColors.primary, width: 1),
                      ),
                      child: Text(
                        'Reset',
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            const Divider(),
            const Text(
              'Berdasarkan :',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Sort options matching Swift implementation exactly
            // 1 : Nilai BID Tertinggi,
            // 2 : Jumlah BID Tertinggi,
            // 3 : Menjelang Close,
            // 4 : Tayang Terbaru
            _buildRadioOption('1', 'Nilai BID Tertinggi'),
            _buildRadioOption('2', 'Jumlah BID Tertinggi'),
            _buildRadioOption('3', 'Menjelang Close Terdekat'),
            _buildRadioOption('4', 'Tayang Terbaru'),

            const SizedBox(height: 24),

            // Apply button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  widget.onSortSelected(selectedSort);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.check, color: Colors.white, size: 18),
                    SizedBox(width: 8),
                    Text(
                      'Terapkan',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioOption(String value, String label) {
    return InkWell(
      onTap: () {
        setState(() {
          selectedSort = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: selectedSort,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedSort = newValue;
                  });
                }
              },
              activeColor: AppColors.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
