import 'package:flutter/material.dart';
import '../utils/colors.dart';

class MenuCardItem extends StatelessWidget {
  final IconData icon;
  final String text;
  final VoidCallback onTap;
  final bool showNew;
  final bool showPita;
  final bool showAuto;
  final Color? iconColor;

  const MenuCardItem({
    Key? key,
    required this.icon,
    required this.text,
    required this.onTap,
    this.showNew = false,
    this.showPita = false,
    this.showAuto = false,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            margin: const EdgeInsets.all(4),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: iconColor ?? AppColors.primary,
                ),
                const SizedBox(height: 8),
                Text(
                  text,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          
          // New badge
          if (showNew)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  'NEW',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
          // Pita badge
          if (showPita)
            Positioned(
              top: 0,
              left: 0,
              child: Image.asset(
                'assets/images/logo_new.png',
                height: 40,
              ),
            ),
            
          // Auto badge
          if (showAuto)
            Positioned(
              top: 0,
              left: 0,
              child: Image.asset(
                'assets/images/logo_auto.png',
                height: 40,
              ),
            ),
        ],
      ),
    );
  }
}
