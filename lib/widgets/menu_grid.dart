import 'package:flutter/material.dart';

class MenuGridItem {
  final IconData icon;
  final String text;
  final String? url;
  final String group;

  MenuGridItem({
    required this.icon,
    required this.text,
    this.url,
    required this.group,
  });
}

class MenuGrid extends StatelessWidget {
  final List<MenuGridItem> menuItems;
  final Function(String, String, {String? url}) onMenuTap;
  final VoidCallback onSellerRegistration;
  final bool isLoadingRegistration;

  const MenuGrid({
    Key? key,
    required this.menuItems,
    required this.onMenuTap,
    required this.onSellerRegistration,
    required this.isLoadingRegistration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      mainAxisSpacing: 8.0,
      crossAxisSpacing: 8.0,
      children: menuItems.map((item) {
        return _buildMenuItem(
          icon: item.icon,
          text: item.text,
          url: item.url,
          group: item.group,
          isRegistrationItem: item.text == 'Pendaftaran Seller',
        );
      }).toList(),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String text,
    String? url,
    required String group,
    required bool isRegistrationItem,
  }) {
    return InkWell(
      onTap: () {
        if (isRegistrationItem) {
          onSellerRegistration();
        } else {
          onMenuTap(text, group, url: url);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isRegistrationItem && isLoadingRegistration)
              const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              Icon(icon, size: 24),
            const SizedBox(height: 8),
            Text(
              text,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
