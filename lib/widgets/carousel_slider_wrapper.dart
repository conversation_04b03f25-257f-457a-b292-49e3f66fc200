import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Wrapper untuk CarouselSlider yang mengatasi konflik import
/// antara package:carousel_slider/carousel_controller.dart dan package:flutter/src/material/carousel.dart
class CarouselSliderWrapper extends StatefulWidget {
  final List<Widget>? items;
  final List<String>? imageUrls;
  final double height;
  final bool autoPlay;
  final bool enlargeCenterPage;
  final double viewportFraction;
  final Duration autoPlayInterval;
  final Duration autoPlayAnimationDuration;

  const CarouselSliderWrapper({
    super.key,
    this.items,
    this.imageUrls,
    required this.height,
    this.autoPlay = true,
    this.enlargeCenterPage = false,
    this.viewportFraction = 1.0,
    this.autoPlayInterval = const Duration(seconds: 3),
    this.autoPlayAnimationDuration = const Duration(milliseconds: 800),
  }) : assert(items != null || imageUrls != null, 'Either items or imageUrls must be provided');

  @override
  State<CarouselSliderWrapper> createState() => _CarouselSliderWrapperState();
}

class _CarouselSliderWrapperState extends State<CarouselSliderWrapper> {
  late PageController _pageController;
  late Timer? _timer;
  int _currentPage = 0;

  List<Widget> get _displayItems {
    if (widget.items != null) {
      return widget.items!;
    } else if (widget.imageUrls != null) {
      return widget.imageUrls!.map((url) => _buildImageWidget(url)).toList();
    }
    return [];
  }

  Widget _buildImageWidget(String imageUrl) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Icon(Icons.error, size: 50),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: widget.viewportFraction);

    if (widget.autoPlay && _displayItems.isNotEmpty) {
      _timer = Timer.periodic(widget.autoPlayInterval, (timer) {
        if (_currentPage < _displayItems.length - 1) {
          _currentPage++;
        } else {
          _currentPage = 0;
        }

        if (_pageController.hasClients) {
          _pageController.animateToPage(
            _currentPage,
            duration: widget.autoPlayAnimationDuration,
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_displayItems.isEmpty) {
      return SizedBox(
        height: widget.height,
        child: Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: Icon(Icons.image, size: 50),
          ),
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          PageView.builder(
            itemCount: _displayItems.length,
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return _displayItems[index];
            },
          ),
          if (_displayItems.length > 1)
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _displayItems.asMap().entries.map((entry) {
                  return Container(
                    width: 8.0,
                    height: 8.0,
                    margin: const EdgeInsets.symmetric(horizontal: 4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentPage == entry.key
                          ? Colors.white
                          : Colors.white.withOpacity(0.4),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }
}
