import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../config/flavor_config.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/change_pin_screen.dart';
import '../screens/auth/forgot_pin_screen.dart';
import '../screens/auth/phone_input_screen.dart';
import '../screens/web_view_screen.dart';
import '../screens/about_app_screen.dart';
import '../screens/delete_account_screen.dart';
import '../utils/app_utils.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';
import 'drawer_header.dart';

class AppDrawer extends StatelessWidget {
  final double width;
  final bool isOpen;
  final VoidCallback onClose;

  const AppDrawer({
    Key? key,
    required this.width,
    required this.isOpen,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      transform: Matrix4.translationValues(isOpen ? 0 : -width, 0, 0),
      child: Container(
        width: width,
        color: Colors.white,
        child: Column(
          children: [
            // Header
            const AppDrawerHeader(),

            // Menu items
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  // Login/Logout
                  if (!authProvider.isLoggedIn)
                    ListTile(
                      leading: Icon(Icons.login, color: AppColors.primary),
                      title: const Text('Login'),
                      onTap: () {
                        Navigator.pop(context); // Close drawer
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PhoneInputScreen(),
                          ),
                        );
                      },
                    ),

                  const Divider(),

                  // Share & Rate
                  ListTile(
                    leading: Icon(
                      Icons.share_outlined,
                      color: AppColors.primary,
                    ),
                    title: const Text('Share'),
                    onTap: () {
                      Navigator.pop(context); // Close drawer
                      _shareApp();
                    },
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.star_half_rounded,
                      color: AppColors.primary,
                    ),
                    title: const Text('Rate'),
                    onTap: () {
                      Navigator.pop(context); // Close drawer
                      _rateApp();
                    },
                  ),

                  const Divider(),

                  // Website & Info
                  ListTile(
                    leading: Icon(Icons.web, color: AppColors.primary),
                    title: const Text('Website'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => WebViewScreen(
                                url: FlavorConfig.instance.baseUrl,
                                title: 'Website',
                              ),
                        ),
                      );
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.code_rounded, color: AppColors.primary),
                    title: const Text('Developer'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => WebViewScreen(
                                url: AppConstants.developerUrl,
                                title: 'Developer',
                              ),
                        ),
                      );
                    },
                  ),
                  ListTile(
                    leading: Icon(
                      Icons.policy_rounded,
                      color: AppColors.primary,
                    ),
                    title: const Text('Kebijakan Privasi'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => WebViewScreen(
                                url: FlavorConfig.instance.privacyPolicyUrl,
                                title: 'Kebijakan Privasi',
                              ),
                        ),
                      );
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.info, color: AppColors.primary),
                    title: const Text('Tentang App'),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AboutAppScreen(),
                        ),
                      );
                    },
                  ),

                  // PIN Management (only for logged in users)
                  if (authProvider.isLoggedIn) ...[
                    const Divider(),

                    ListTile(
                      leading: Icon(
                        Icons.lock_person_rounded,
                        color: AppColors.primary,
                      ),
                      title: const Text('Ubah PIN'),
                      onTap: () {
                        Navigator.pop(context); // Close drawer
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ChangePinScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: Icon(
                        Icons.lock_open_rounded,
                        color: AppColors.primary,
                      ),
                      title: const Text('Lupa PIN'),
                      onTap: () {
                        Navigator.pop(context); // Close drawer
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ForgotPinScreen(),
                          ),
                        );
                      },
                    ),

                    const Divider(),

                    // Logout
                    ListTile(
                      leading: const Icon(Icons.logout, color: Colors.red),
                      title: const Text('Logout'),
                      onTap: () {
                        Navigator.pop(context); // Close drawer
                        _showLogoutConfirmation(context, authProvider);
                      },
                    ),

                    const Divider(),

                    // Delete Account
                    ListTile(
                      leading: const Icon(
                        Icons.person_remove_alt_1,
                        color: Colors.red,
                      ),
                      title: const Text('Hapus Akun'),
                      onTap: () {
                        Navigator.pop(context); // Close drawer
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DeleteAccountScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }


  Future<void> _shareApp() async {
    await AppUtils.shareApp();
  }

  Future<void> _rateApp() async {
    await AppUtils.rateApp();
  }


  void _showLogoutConfirmation(
    BuildContext context,
    AuthProvider authProvider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Konfirmasi Logout'),
            content: const Text('Apakah Anda yakin ingin keluar?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Batal'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  authProvider.logout();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
    );
  }
}
