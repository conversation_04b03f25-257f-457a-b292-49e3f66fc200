import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class SideMenu extends StatelessWidget {
  final double width;
  final bool isOpen;
  final VoidCallback onClose;
  final String viewNama;
  final String noWa;

  const SideMenu({
    Key? key,
    required this.width,
    required this.isOpen,
    required this.onClose,
    required this.viewNama,
    required this.noWa,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      transform: Matrix4.translationValues(
        isOpen ? 0 : -width,
        0,
        0,
      ),
      child: Container(
        width: width,
        color: Colors.white,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
              color: Theme.of(context).primaryColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 40),
                  Text(
                    viewNama,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    noWa,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildMenuItem(
                    icon: FontAwesomeIcons.house,
                    title: 'Home',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: FontAwesomeIcons.creditCard,
                    title: 'Rekpen 15K',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: FontAwesomeIcons.fish,
                    title: 'Ikan Lelang',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: FontAwesomeIcons.userFriends,
                    title: 'Bidder',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: FontAwesomeIcons.store,
                    title: 'Seller',
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: FaIcon(icon, size: 20),
      title: Text(title),
      onTap: onTap,
    );
  }
}
