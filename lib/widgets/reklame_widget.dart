import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'package:url_launcher/url_launcher.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/flavor_config.dart';
import '../models/reklame.dart';
import '../utils/constants.dart'; // Assuming you have constants file with baseUrl

class ReklameWidget extends StatefulWidget {
  final List<Reklame> reklameList;
  final double height;
  final String? idLelang;
  final String? idObyekLelang;

  const ReklameWidget({
    Key? key,
    this.reklameList = const [],
    this.height = 120,
    this.idLelang,
    this.idObyekLelang,
  }) : super(key: key);

  @override
  State<ReklameWidget> createState() => _ReklameWidgetState();
}

class _ReklameWidgetState extends State<ReklameWidget> {
  late PageController _pageController;
  late Timer _timer;
  int _currentIndex = 0;
  bool _isLoading = true;
  List<Reklame> _reklameList = [];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _reklameList = widget.reklameList;
    
    if (_reklameList.isEmpty) {
      _fetchReklameData();
    } else {
      _isLoading = false;
    }
    
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (mounted && _reklameList.isNotEmpty) {
        _currentIndex = (_currentIndex + 1) % _reklameList.length;
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Future<void> _fetchReklameData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';
      final baseUrl = FlavorConfig.instance.baseUrl; // Assuming you have this in constants
      
      final url = Uri.parse('$baseUrl/api/reklame');
      final headers = {
        'Content-Type': 'application/json',
        'Session': session,
      };
      
      final params = {
        'id_lelang': widget.idLelang ?? '',
        'id_obyek_lelang': widget.idObyekLelang ?? '',
      };
      
      final response = await http.get(
        Uri.parse('$url${_buildQueryString(params)}'),
        headers: headers,
      );
      
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        
        if (jsonData['meta']['code'] == 200) {
          final items = jsonData['data'] as List;
          setState(() {
            _reklameList = items.map((item) {
              final reklame = Reklame.fromJson(item);
              
              // Update Firebase info
              _updateFirebaseInfo(reklame);
              
              return reklame;
            }).toList();
            
            // Shuffle the list like in Swift version
            _reklameList.shuffle();
            _isLoading = false;
          });
        } else {
          setState(() => _isLoading = false);
        }
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      debugPrint('Error fetching reklame data: $e');
      setState(() => _isLoading = false);
    }
  }

  String _buildQueryString(Map<String, String> params) {
    if (params.isEmpty) return '';
    
    final queryParams = params.entries
        .where((e) => e.value.isNotEmpty)
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return queryParams.isNotEmpty ? '?$queryParams' : '';
  }

  void _updateFirebaseInfo(Reklame reklame) {
    if (reklame.countImpression <= 0) return;
    
    final database = FirebaseDatabase.instance.ref();
    final safeToken = reklame.tokenReklame
        .replaceAll('.', '_')
        .replaceAll('#', '_')
        .replaceAll('\$', '_')
        .replaceAll('[', '_')
        .replaceAll(']', '_');
    
    final infoRef = database
        .child('reklame_analytics')
        .child(safeToken);
    
    infoRef.update({
      'img_reklame_1': reklame.imgReklame1,
      'inputer': reklame.inputer,
      'url_img_reklame_2': reklame.urlImgReklame2,
      'slider_show': reklame.sliderShow,
      'url_call_to_action': reklame.urlCallToAction,
      'count_impression': reklame.countImpression,
      'url_img_reklame_1': reklame.urlImgReklame1,
      'id_reklame': reklame.idReklame,
      'img_version': reklame.imgVersion,
      'qty_call_to_action': reklame.qtyCallToAction,
      'urutan': reklame.urutan,
      'judul_reklame': reklame.judulReklame,
      'tgl_tayang_awal': reklame.tglTayangAwal,
      'title_call_to_action': reklame.titleCallToAction,
      'aktif': reklame.aktif,
      'img_reklame_2': reklame.imgReklame2,
      'token_reklame': reklame.tokenReklame,
      'tgl_tayang_akhir': reklame.tglTayangAkhir,
      'input_timer': reklame.inputTimer
    });
  }

  void _logReklameImpression(Reklame reklame) {
    if (reklame.countImpression <= 0) return;
    
    final database = FirebaseDatabase.instance.ref();
    final dateTime = DateTime.now();
    final today = '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';

    final safeToken = reklame.tokenReklame
        .replaceAll('.', '_')
        .replaceAll('#', '_')
        .replaceAll('\$', '_')
        .replaceAll('[', '_')
        .replaceAll(']', '_');

    final analyticsRef = database
        .child('reklame_analytics')
        .child(safeToken)
        .child('daily')
        .child(today);

    analyticsRef.update({
      'views': ServerValue.increment(1),
      'last_updated': ServerValue.timestamp,
    });
  }

  void _logReklameClick(Reklame reklame) async {
    if (reklame.countImpression <= 0) return;
    
    final database = FirebaseDatabase.instance.ref();
    final dateTime = DateTime.now();
    final today = '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';

    final safeToken = reklame.tokenReklame
        .replaceAll('.', '_')
        .replaceAll('#', '_')
        .replaceAll('\$', '_')
        .replaceAll('[', '_')
        .replaceAll(']', '_');

    final analyticsRef = database
        .child('reklame_analytics')
        .child(safeToken)
        .child('daily')
        .child(today);

    analyticsRef.update({
      'clicks': ServerValue.increment(1),
      'last_updated': ServerValue.timestamp,
    });

    final url = Uri.parse(reklame.urlCallToAction);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return SizedBox(
        height: widget.height,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_reklameList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        SizedBox(
          height: widget.height,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
                _logReklameImpression(_reklameList[index]);
              });
            },
            itemCount: _reklameList.length,
            itemBuilder: (context, index) {
              final reklame = _reklameList[index];
              return GestureDetector(
                onTap: () => _logReklameClick(reklame),
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: reklame.urlImgReklame2,
                      height: widget.height,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      errorWidget: (context, url, error) => const Icon(Icons.error),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 8,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _reklameList.length,
              (index) => Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentIndex == index
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}