import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/colors.dart';

class FilterDialog extends StatefulWidget {
  final List<dynamic> sizeIkanList;
  final List<dynamic> levelSellerList;
  final List<dynamic> modeLelangList;
  final List<dynamic> lokasiIkanList;
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onApplyFilter;
  final VoidCallback? onReset;

  const FilterDialog({
    Key? key,
    required this.sizeIkanList,
    required this.levelSellerList,
    required this.modeLelangList,
    required this.lokasiIkanList,
    required this.currentFilters,
    required this.onApplyFilter,
    this.onReset,
  }) : super(key: key);

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late Map<String, dynamic> selectedFilters;

  // Price range controllers
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  // Rating filter
  String _rating = '';

  // Gender filter
  String _gender = '';

  // Status filter
  String _statusBid = '';

  // Close timer filter
  String _menjelangClose = '';

  // Priority filter
  String _priority = '';

  // Mode lelang filter
  String _modeLelang = '';

  // Multiple selection filters
  List<String> _selectedLevelSeller = [];
  List<String> _selectedLokasiIkan = [];
  List<String> _selectedSizeIkan = [];

  @override
  void initState() {
    super.initState();
    selectedFilters = Map.from(widget.currentFilters);

    // Initialize controllers with existing values
    _minPriceController.text = selectedFilters['min_price']?.toString() ?? '';
    _maxPriceController.text = selectedFilters['max_price']?.toString() ?? '';

    // Initialize filter values from current filters
    _rating = selectedFilters['rating'] ?? '';
    _gender = selectedFilters['gender'] ?? '';
    _statusBid = selectedFilters['status_bid'] ?? '';
    _menjelangClose = selectedFilters['menjelang_close'] ?? '';
    _priority = selectedFilters['priority'] ?? '';
    _modeLelang = selectedFilters['mode_lelang'] ?? '';

    // Initialize multiple selection filters
    _selectedLevelSeller = List<String>.from(selectedFilters['selectedIDs_level_seller'] ?? []);
    _selectedLokasiIkan = List<String>.from(selectedFilters['selectedIDs_lokasi_ikan'] ?? []);
    _selectedSizeIkan = List<String>.from(selectedFilters['selectedIDs_size_ikan'] ?? []);
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'FILTER',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _rating = '';
                            _gender = '';
                            _statusBid = '';
                            _menjelangClose = '';
                            _priority = '';
                            _modeLelang = '';
                            _selectedLevelSeller.clear();
                            _selectedLokasiIkan.clear();
                            _selectedSizeIkan.clear();
                            _minPriceController.clear();
                            _maxPriceController.clear();
                          });
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          side: BorderSide(color: AppColors.primary, width: 1),
                        ),
                        child: Text(
                          'Clear',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () {
                          if (widget.onReset != null) {
                            widget.onReset!();
                          }
                          Navigator.pop(context);
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          side: BorderSide(color: AppColors.primary, width: 1),
                        ),
                        child: Text(
                          'Reset',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Size Filter
              if (widget.sizeIkanList.isNotEmpty) ...[
                const Text(
                  'Ukuran Ikan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.sizeIkanList.map((sizeItem) {
                    final sizeId = sizeItem['id']?.toString() ?? '';
                    final sizeName = sizeItem['nama_size_ikan']?.toString() ?? sizeItem.toString();
                    final isSelected = _selectedSizeIkan.contains(sizeId);
                    return FilterChip(
                      label: Text(sizeName),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            if (!_selectedSizeIkan.contains(sizeId)) {
                              _selectedSizeIkan.add(sizeId);
                            }
                          } else {
                            _selectedSizeIkan.remove(sizeId);
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Seller Level Filter
              if (widget.levelSellerList.isNotEmpty) ...[
                const Text(
                  'Level Seller',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.levelSellerList.map((levelItem) {
                    final levelId = levelItem['id']?.toString() ?? '';
                    final levelName = levelItem['nama_level_seller']?.toString() ?? levelItem.toString();
                    final isSelected = _selectedLevelSeller.contains(levelId);
                    return FilterChip(
                      label: Text(levelName),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            if (!_selectedLevelSeller.contains(levelId)) {
                              _selectedLevelSeller.add(levelId);
                            }
                          } else {
                            _selectedLevelSeller.remove(levelId);
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Mode Lelang Filter
              if (widget.modeLelangList.isNotEmpty) ...[
                const Text(
                  'Mode Lelang',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.modeLelangList.map((modeItem) {
                    final modeId = modeItem['id']?.toString() ?? '';
                    final modeName = modeItem['nama_mode_lelang']?.toString() ?? modeItem.toString();
                    final isSelected = _modeLelang == modeId;
                    return FilterChip(
                      label: Text(modeName),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _modeLelang = modeId;
                          } else {
                            _modeLelang = '';
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Location Filter
              if (widget.lokasiIkanList.isNotEmpty) ...[
                const Text(
                  'Lokasi Ikan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.lokasiIkanList.map((lokasiItem) {
                    final lokasiId = lokasiItem['id']?.toString() ?? '';
                    final lokasiName = lokasiItem['nama_lokasi_ikan']?.toString() ?? lokasiItem.toString();
                    final isSelected = _selectedLokasiIkan.contains(lokasiId);
                    return FilterChip(
                      label: Text(lokasiName),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            if (!_selectedLokasiIkan.contains(lokasiId)) {
                              _selectedLokasiIkan.add(lokasiId);
                            }
                          } else {
                            _selectedLokasiIkan.remove(lokasiId);
                          }
                        });
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Price Range Filter
              const Text(
                'Rentang Harga',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _minPriceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: const InputDecoration(
                        labelText: 'Harga Min',
                        border: OutlineInputBorder(),
                        prefixText: 'Rp ',
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          selectedFilters['min_price'] = int.tryParse(value) ?? 0;
                        } else {
                          selectedFilters.remove('min_price');
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: _maxPriceController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: const InputDecoration(
                        labelText: 'Harga Max',
                        border: OutlineInputBorder(),
                        prefixText: 'Rp ',
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          selectedFilters['max_price'] = int.tryParse(value) ?? 0;
                        } else {
                          selectedFilters.remove('max_price');
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Rating Filter
              const Text(
                'Rating Seller Minimum',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['1', '2', '3', '4', '5'].map((rating) {
                  final isSelected = _rating == rating;
                  return FilterChip(
                    label: Text('$rating ⭐'),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _rating = rating;
                        } else {
                          _rating = '';
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Gender Filter
              const Text(
                'Jenis Kelamin',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['Jantan', 'Betina', 'Tidak Diketahui'].map((gender) {
                  final isSelected = _gender == gender;
                  return FilterChip(
                    label: Text(gender),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _gender = gender;
                        } else {
                          _gender = '';
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Status Bid Filter
              const Text(
                'Status Bid',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['Sudah Bid', 'Belum Bid', 'Semua'].map((status) {
                  final isSelected = _statusBid == status;
                  return FilterChip(
                    label: Text(status),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _statusBid = status;
                        } else {
                          _statusBid = '';
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Close Timer Filter
              const Text(
                'Menjelang Tutup',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['5 Menit', '10 Menit', '30 Menit', '1 Jam'].map((timer) {
                  final isSelected = _menjelangClose == timer;
                  return FilterChip(
                    label: Text(timer),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _menjelangClose = timer;
                        } else {
                          _menjelangClose = '';
                        }
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? AppColors.primary : Colors.black87,
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // Apply Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    // Construct filters matching Swift implementation
                    final filters = <String, dynamic>{};

                    // Price range filter
                    if (_minPriceController.text.isNotEmpty && _maxPriceController.text.isNotEmpty) {
                      filters['range_last_value_bid'] = '${_minPriceController.text},${_maxPriceController.text}';
                    }

                    // Single selection filters
                    if (_rating.isNotEmpty) filters['rating'] = _rating;
                    if (_gender.isNotEmpty) filters['gender'] = _gender;
                    if (_statusBid.isNotEmpty) filters['status_bid'] = _statusBid;
                    if (_menjelangClose.isNotEmpty) filters['menjelang_close'] = _menjelangClose;
                    if (_priority.isNotEmpty) filters['priority'] = _priority;
                    if (_modeLelang.isNotEmpty) filters['mode_lelang'] = _modeLelang;

                    // Multiple selection filters
                    if (_selectedLevelSeller.isNotEmpty) filters['selectedIDs_level_seller'] = _selectedLevelSeller;
                    if (_selectedLokasiIkan.isNotEmpty) filters['selectedIDs_lokasi_ikan'] = _selectedLokasiIkan;
                    if (_selectedSizeIkan.isNotEmpty) filters['selectedIDs_size_ikan'] = _selectedSizeIkan;

                    widget.onApplyFilter(filters);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: const [
                      Icon(Icons.check, color: Colors.white, size: 18),
                      SizedBox(width: 8),
                      Text(
                        'Terapkan',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
