import 'package:flutter/material.dart';
import '../models/ikan_lelang.dart';
import '../services/api_service.dart';
import '../utils/colors.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// RelatedFishListingsView widget
/// Displays a horizontal list of related fish listings
class RelatedFishListingsView extends StatefulWidget {
  final String title;
  final String endpoint;
  final String idObyekLelang;
  final String idLelang;

  const RelatedFishListingsView({
    Key? key,
    required this.title,
    required this.endpoint,
    required this.idObyekLelang,
    this.idLelang = '',
  }) : super(key: key);

  @override
  State<RelatedFishListingsView> createState() => _RelatedFishListingsViewState();
}

class _RelatedFishListingsViewState extends State<RelatedFishListingsView> {
  final List<IkanLelang> _fishList = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _page = 1;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!_hasMoreData || _isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final queryParameters = <String, String>{
        'page': _page.toString(),
        'per_page': '25',
      };

      if (widget.idLelang.isNotEmpty) {
        queryParameters['id_lelang'] = widget.idLelang;
        queryParameters['id_obyek_lelang'] = widget.idObyekLelang;
      }

      final response = await ApiService().get(
        widget.endpoint,
        queryParameters: queryParameters,
      );

      if (response['meta']['code'] == 200) {
        final items = response['data'] as List;
        final newFish = items.map((item) => IkanLelang.fromJson(item)).toList();

        setState(() {
          if (_page == 1) {
            _fishList.clear();
          }
          _fishList.addAll(newFish);
          _hasMoreData = newFish.isNotEmpty;
          _page++;
          _isLoading = false;
        });
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = response['meta']['message'] ?? 'Unknown error';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_fishList.isEmpty && !_isLoading) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            widget.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // Fish list
        SizedBox(
          height: 220, // Fixed height for horizontal scrolling
          child: _isLoading && _fishList.isEmpty
              ? const Center(child: CircularProgressIndicator())
              : ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _fishList.length + (_hasMoreData ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _fishList.length) {
                      _loadData(); // Load more when reaching the end
                      return const SizedBox(
                        width: 60,
                        child: Center(
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      );
                    }

                    final fish = _fishList[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 12),
                      child: _FishCard(
                        fish: fish,
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            '/ikan-detail',
                            arguments: fish.idObyekLelang,
                          );
                        },
                      ),
                    );
                  },
                ),
        ),

        const SizedBox(height: 8),
      ],
    );
  }
}

class _FishCard extends StatelessWidget {
  final IkanLelang fish;
  final VoidCallback onTap;

  const _FishCard({
    Key? key,
    required this.fish,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Fish image
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  child: Container(
                    height: 120,
                    width: double.infinity,
                    color: AppColors.darkBlue,
                    child: CachedNetworkImage(
                      imageUrl: fish.viewFoto,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.image_not_supported,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Auction mode label if KC
                if (int.parse(fish.ikanKc) >= 1)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      color: AppColors.darkBlue,
                      child: Text(
                        fish.kodeLelangMode,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Fish details
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fish.namaObyekLelang,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    fish.viewValueOb,
                    style: const TextStyle(
                      color: Color(0xFF11254E),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    fish.viewLastValueBid.isNotEmpty
                        ? fish.viewLastValueBid
                        : 'Belum ada bid',
                    style: TextStyle(
                      color: fish.viewLastValueBid.isNotEmpty
                          ? Colors.green
                          : Colors.grey,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}