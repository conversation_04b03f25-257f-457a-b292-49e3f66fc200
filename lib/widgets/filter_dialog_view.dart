import 'package:flutter/material.dart';
import '../../models/filter_models.dart';
import '../../utils/constants.dart';
import '../config/flavor_config.dart';

class FilterDialogView extends StatefulWidget {
  final List<SizeIkan> sizeIkanList;
  final List<LevelSeller> levelSellerList;
  final List<ModeLelang> modeLelangList;
  final List<LokasiIkan> lokasiIkanList;
  final List<String> selectedLevelSeller;
  final List<String> selectedLokasiIkan;
  final List<String> selectedSizeIkan;
  final String rating;
  final String rangeLastValueBid;
  final String priority;
  final String modeLelang;
  final String gender;
  final String statusBid;
  final String menjelangClose;
  final String kotaSeller;
  final RangeValues sliderPosition;
  final int minLastValueBid;
  final int maxLastValueBid;
  final VoidCallback onReset;
  final Function(String, String) onApplyFilter;

  const FilterDialogView({
    Key? key,
    required this.sizeIkanList,
    required this.levelSellerList,
    required this.modeLelangList,
    required this.lokasiIkanList,
    required this.selectedLevelSeller,
    required this.selectedLokasiIkan,
    required this.selectedSizeIkan,
    required this.rating,
    required this.rangeLastValueBid,
    required this.priority,
    required this.modeLelang,
    required this.gender,
    required this.statusBid,
    required this.menjelangClose,
    required this.kotaSeller,
    required this.sliderPosition,
    required this.minLastValueBid,
    required this.maxLastValueBid,
    required this.onReset,
    required this.onApplyFilter,
  }) : super(key: key);

  @override
  State<FilterDialogView> createState() => _FilterDialogViewState();
}

class _FilterDialogViewState extends State<FilterDialogView> {
  late List<String> _selectedLevelSeller;
  late List<String> _selectedLokasiIkan;
  late List<String> _selectedSizeIkan;
  late String _rating;
  late String _priority;
  late String _modeLelang;
  late String _gender;
  late String _statusBid;
  late String _menjelangClose;
  late RangeValues _currentRange;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    _selectedLevelSeller = List.from(widget.selectedLevelSeller);
    _selectedLokasiIkan = List.from(widget.selectedLokasiIkan);
    _selectedSizeIkan = List.from(widget.selectedSizeIkan);
    _rating = widget.rating;
    _priority = widget.priority;
    _modeLelang = widget.modeLelang;
    _gender = widget.gender;
    _statusBid = widget.statusBid;
    _menjelangClose = widget.menjelangClose;
    _currentRange = widget.sliderPosition;
  }

  void _clearFilters() {
    setState(() {
      _selectedLevelSeller.clear();
      _selectedLokasiIkan.clear();
      _selectedSizeIkan.clear();
      _rating = '';
      _priority = '';
      _modeLelang = '';
      _gender = '';
      _statusBid = '';
      _menjelangClose = '';
      _currentRange = RangeValues(
        widget.minLastValueBid.toDouble(),
        widget.maxLastValueBid.toDouble(),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildRatingSection(),
                  const Divider(),
                  _buildLevelSellerSection(),
                  const Divider(),
                  _buildPrioritySection(),
                  if (widget.modeLelangList.isNotEmpty) ...[
                    const Divider(),
                    //_buildModeLelangSection(),
                  ],
                  const Divider(),
                  //_buildNilaiLelangSection(),
                  const Divider(),
                  //_buildGenderSection(),
                  const Divider(),
                  //_buildStatusBidSection(),
                  const Divider(),
                  //_buildMenjelangCloseSection(),
                  const Divider(),
                  //_buildSizeSection(),
                  const Divider(),
                  //_buildLokasiSection(),
                ],
              ),
            ),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          'FILTER',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear'),
        ),
        TextButton(
          onPressed: widget.onReset,
          child: const Text('Reset'),
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Rating Seller',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        CheckboxListTile(
          title: const Text('Seller ⭐️⭐️⭐️'),
          value: _rating.contains('3'),
          onChanged: (value) => _updateRating('3', value ?? false),
        ),
        CheckboxListTile(
          title: const Text('Seller ⭐️⭐️⭐️⭐️'),
          value: _rating.contains('4'),
          onChanged: (value) => _updateRating('4', value ?? false),
        ),
        CheckboxListTile(
          title: const Text('Seller ⭐️⭐️⭐️⭐️⭐️'),
          value: _rating.contains('5'),
          onChanged: (value) => _updateRating('5', value ?? false),
        ),
      ],
    );
  }

  Widget _buildLevelSellerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Level Seller',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        ...widget.levelSellerList.map((level) {
          return CheckboxListTile(
            title: Row(
              children: [
                Image.network(
                  level.iconUrl,
                  width: 24,
                  height: 24,
                  errorBuilder: (_, __, ___) => const Icon(Icons.error),
                ),
                const SizedBox(width: 8),
                Text(level.nama),
              ],
            ),
            value: _selectedLevelSeller.contains(level.id),
            onChanged: (value) {
              setState(() {
                if (value ?? false) {
                  _selectedLevelSeller.add(level.id);
                } else {
                  _selectedLevelSeller.remove(level.id);
                }
              });
            },
          );
        }),
      ],
    );
  }

  Widget _buildPrioritySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Kategori Seller',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        CheckboxListTile(
          title: const Text('PRIORITY'),
          value: _priority == '1',
          onChanged: (value) {
            setState(() {
              _priority = value ?? false ? '1' : '';
            });
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: () {
            widget.onApplyFilter(
              _currentRange.start.toString(),
              _currentRange.end.toString(),
            );
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
           // backgroundColor: FlavorConfig.instance.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check),
              SizedBox(width: 8),
              Text('Terapkan'),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () {
            _clearFilters();
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.arrow_back),
              SizedBox(width: 8),
              Text('Batal'),
            ],
          ),
        ),
      ],
    );
  }

  void _updateRating(String value, bool isChecked) {
    setState(() {
      if (isChecked) {
        _rating += '$value,';
      } else {
        _rating = _rating
            .split(',')
            .where((r) => r != value)
            .join(',');
      }
      _rating = _rating.replaceAll(RegExp(r',+$'), '');
    });
  }

  // Additional widget builders for ModeLelang, NilaiLelang, Gender, StatusBid,
  // MenjelangClose, Size, and Lokasi sections would go here...
}
