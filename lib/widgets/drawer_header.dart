import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/flavor_config.dart';
import '../providers/auth_provider.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class AppDrawerHeader extends StatelessWidget {
  const AppDrawerHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          height: 160,
          child: Stack(
            children: [
              // Top bar
              // Background image
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                child: Image.asset(
                  'assets/images/'+FlavorConfig.instance.flavor.name+'/gambar1.png',
                  fit: BoxFit.cover,
                ),
              ),
              
              // Overlay
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  color: Colors.black.withOpacity(0.6),
                ),
              ),
              
              // Avatar
              Positioned(
                top: 24,
                left: 16,
                child: CircleAvatar(
                  radius: 34,
                  backgroundImage: AssetImage(FlavorConfig.instance.logoPath),
                ),
              ),
              
              // User info
              Positioned(
                left: 16,
                right: 16,
                bottom: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      authProvider.isLoggedIn 
                          ? (authProvider.user?.viewNama ?? FlavorConfig.instance.appName)
                          : FlavorConfig.instance.appName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text( 'Anda Belum Login',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
