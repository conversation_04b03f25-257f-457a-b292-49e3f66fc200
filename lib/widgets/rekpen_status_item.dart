import 'package:flutter/material.dart';

import '../models/rekpen_status.dart';

class RekpenStatusItem extends StatelessWidget {
  final RekpenStatus status;
  final bool isSelected;
  final VoidCallback onTap;

  const RekpenStatusItem({
    Key? key,
    required this.status,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Parse warna dari string hex
    Color bgColor = Color(int.parse('0xFF${status.colorBgStatusPayment.replaceAll('#', '')}'));
    Color textColor = Color(int.parse('0xFF${status.colorTextStatusPayment.replaceAll('#', '')}'));

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
      child: Material(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        elevation: 2,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            child: Text(
              status.namaStatusPaymentIndonesia,
              style: TextStyle(
                color: textColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
