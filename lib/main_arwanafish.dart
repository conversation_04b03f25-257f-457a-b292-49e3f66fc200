import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'config/flavor_config.dart';
import 'config/firebase_config.dart';
import 'main.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorConfig(
    flavor: Flavor.arwanafish,
    name: 'Arwana Fish',
    appId: 'com.aronesia.bid',
    appStoreId: '1234567890', // Ganti dengan ID App Store yang sesuai
    appName: 'Arwana Fish',
    baseUrl: 'https://aronesia.bid',
    privacyPolicyUrl: 'https://aronesia.bid/privacy-policy',
    appStoreUrl: '',
    logoPath: 'assets/images/logo_arwanafish.png',
    primaryColor: '#1A1A1A', // Hitam
    accentColor: '#66BB6A', // Hijau
  );

  // Initialize Firebase
  await FirebaseConfig.initializeFirebase();

  // Get stored preferences
  final prefs = await SharedPreferences.getInstance();
  final String languageCode = prefs.getString('language_code') ?? 'en';
  final bool hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  // Run the app
  runApp(MasterKoiApp(
    initialLocale: Locale(languageCode),
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}
