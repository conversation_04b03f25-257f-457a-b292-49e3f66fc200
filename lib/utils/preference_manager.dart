import 'package:shared_preferences/shared_preferences.dart';

class PreferenceManager {
  static final PreferenceManager _instance = PreferenceManager._internal();
  factory PreferenceManager() => _instance;
  PreferenceManager._internal();

  late SharedPreferences _preferences;
  bool _initialized = false;

  Future<void> init() async {
    if (!_initialized) {
      _preferences = await SharedPreferences.getInstance();
      _initialized = true;
    }
  }

  // Save string preference
  Future<bool> savePreference(String key, String value) async {
    await init();
    return await _preferences.setString(key, value);
  }

  // Save double preference
  Future<bool> savePreferenceDouble(String key, double value) async {
    await init();
    return await _preferences.setDouble(key, value);
  }

  // Load string preference
  String loadPreference(String key) {
    if (!_initialized) return '';
    return _preferences.getString(key) ?? '';
  }

  // Load double preference
  double loadPreferenceDouble(String key) {
    if (!_initialized) return 0.0;
    return _preferences.getDouble(key) ?? 0.0;
  }

  // Save access token related data
  Future<bool> saveAccessToken({
    required String accessToken,
    required String tokenType,
    required String refreshToken,
    required String scope,
  }) async {
    await init();

    bool success = true;
    success &= await _preferences.setString('access_token', accessToken);
    success &= await _preferences.setString('token_type', tokenType);
    success &= await _preferences.setString('refresh_token', refreshToken);
    success &= await _preferences.setString('scope', scope);

    return success;
  }

  // Get access token
  String getAccessToken() {
    if (!_initialized) return '';
    return _preferences.getString('access_token') ?? '';
  }

  // Clear all preferences
  Future<bool> clearAll() async {
    await init();
    return await _preferences.clear();
  }

  // Common keys used in the app
  static const String sessionKey = 'session';
  static const String viewNamaKey = 'view_nama';
  static const String noWaKey = 'no_wa';
  static const String viewUrlFotoLogoKey = 'view_url_foto_logo';
  static const String listSellerKey = 'list_seller';
  static const String namaPemilikSellerKey = 'nama_pemilik_seller';
  static const String namaBuyerKey = 'nama_buyer';
  static const String listBuyerKey = 'list_buyer';
  static const String noHpBuyerKey = 'no_hp_buyer';
  static const String asBnrKey = 'as_bnr';
  static const String nomorBantuanKey = 'nomor_bantuan';
}
