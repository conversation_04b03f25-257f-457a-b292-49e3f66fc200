import 'package:flutter/material.dart';
import '../config/flavor_config.dart';

class HeaderBanner extends StatelessWidget {
  final String? imageNumber;
  final String text;
  final double height;

  const HeaderBanner({
    Key? key,
    this.imageNumber,
    this.text = 'Terintegrasi Real-Time antara WhatsApp dan <PERSON>, serta terpublis di Official Web 😎',
    this.height = 160,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
           "assets/images/${FlavorConfig.instance.flavor.name}/$imageNumber",
          height: height,
          width: double.infinity,
          fit: BoxFit.cover,
        ),
        Container(
          height: height,
          color: Colors.black.withOpacity(0.6),
        ),
        SizedBox(
          height: height,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                text,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

