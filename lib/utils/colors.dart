import 'package:flutter/material.dart';
import '../config/flavor_config.dart';

class AppColors {
  // Dynamic colors based on flavor (like Swift FlavorStrings)
  static Color get primary {
    try {
      final config = FlavorConfig.instance;
      return _hexToColor(config.primaryColor);
    } catch (e) {
      return const Color(0xFF11254E); // Default darkblue from Swift
    }
  }

  static Color get accent {
    try {
      final config = FlavorConfig.instance;
      return _hexToColor(config.accentColor);
    } catch (e) {
      return const Color(0xFF66BB6A); // Default green from Swift
    }
  }

  // Swift-inspired color scheme
  static const Color darkBlue = Color(0xFF11254E); // From Swift darkblue.colorset
  static const Color darkGrey = Color(0xFF323232); // From Swift darkgrey.colorset
  static const Color lightBlue = Color(0xFF78BBC2); // From Swift lightblue.colorset
  static const Color darkGreen = Color(0xFF008000); // From Swift darkgreen.colorset

  // Legacy colors for backward compatibility
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color primaryDark = Color(0xFF1565C0);

  // Text colors (consistent with Swift)
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textWhite = Colors.white;

  // System colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFFA000);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Background colors (matching Swift patterns)
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Colors.white;
  static const Color surfaceVariant = Color(0xFFF7F7F7);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color overlay = Color(0x99000000); // 60% black overlay

  // Status colors for bidding (matching Swift logic)
  static const Color bidActive = Color(0xFF4CAF50);
  static const Color bidEnded = Color(0xFF757575);
  static const Color bidComing = Color(0xFF2196F3);
  static const Color bidCanceled = Color(0xFFF44336);

  // Rating colors
  static const Color starActive = Color(0xFFFFB300);
  static const Color starInactive = Color(0xFFE0E0E0);

  // Alert background colors (like Swift)
  static const Color alertSuccess = Color(0xFFEBF7EE);
  static const Color alertWarning = Color(0xFFFEF7EA);
  static const Color alertError = Color(0xFFFCEDEA);
  static const Color alertInfo = Color(0xFFE5EFFA);

  // Helper method to convert hex string to Color
  static Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex'; // Add alpha if not provided
    }
    return Color(int.parse(hex, radix: 16));
  }

  // Flavor-specific colors
  static Color get koifishPrimary => const Color(0xFF11254E); // Biru tua
  static Color get goldfishPrimary => const Color(0xFFFA0707); // Merah
  static Color get arwanafishPrimary => const Color(0xFF1A1A1A); // Hitam

  // Common accent color for all flavors
  static Color get commonAccent => const Color(0xFF66BB6A); // Hijau
}
