import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class WarningMessageView extends StatelessWidget {
  const WarningMessageView({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.red,
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      child: const Text(
        "Mengingat maraknya aksi penipuan saat ini. Master Koi BOT TIDAK BERTANGGUNGJAWAB & TIDAK MENERIMA KOMPLAIN atas transaksi yang TIDAK melalui Rekening Penjamin (Rekpen).",
        style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }
}


class WarningMessagePenipuan extends StatelessWidget {
  const WarningMessagePenipuan({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.red,
      padding: const EdgeInsets.all(8),
      child: const Text(
        "Hati-hati penipuan! Pastikan Anda bertransaksi melalui rekening penjamin resmi.",
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
    );
  }
}
