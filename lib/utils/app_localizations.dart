import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const _localizedValues = <String, Map<String, String>>{
    'en': {
      'select_language': 'Select Language',
      'continue': 'Continue',
      'english': 'English',
      'indonesian': 'Indonesian',
      'choose_language': 'Choose your preferred language',
      'can_change_later': 'You can change this later in settings',
      // Onboarding texts
      'skip': 'Skip',
      'next': 'Next',
      'get_started': 'Get Started',
      // Page 1
      'best_fish_title': 'Best Fish',
      'best_fish_short': 'Premium Fish Collection',
      'best_fish_long': 'Get high-quality koi fish collection that has gone through a strict selection process to ensure the best quality.',
      // Page 2
      'multi_platform_title': 'Multi Platform',
      'multi_platform_short': 'Access Anywhere',
      'multi_platform_long': 'Enjoy easy access to the Master Koi app from various devices, both through mobile and web browser.',
      // Page 3
      'security_title': 'Anti BNR & Guarantee Account',
      'security_short': 'Secure Transaction',
      'security_long': 'Anti Bid and Run system with guarantee account to ensure the security of every transaction you make.',
    },
    'id': {
      'select_language': 'Pilih Bahasa',
      'continue': 'Lanjutkan',
      'english': 'Bahasa Inggris',
      'indonesian': 'Bahasa Indonesia',
      'choose_language': 'Pilih bahasa yang Anda inginkan',
      'can_change_later': 'Anda dapat mengubahnya nanti di pengaturan',
      // Onboarding texts
      'skip': 'Lewati',
      'next': 'Selanjutnya',
      'get_started': 'Mulai',
      // Page 1
      'best_fish_title': 'Ikan Terbaik',
      'best_fish_short': 'Koleksi Ikan Pilihan',
      'best_fish_long': 'Dapatkan koleksi ikan koi berkualitas tinggi yang telah melalui proses seleksi ketat untuk memastikan kualitas terbaik.',
      // Page 2
      'multi_platform_title': 'Multi Platform',
      'multi_platform_short': 'Akses Dimana Saja',
      'multi_platform_long': 'Nikmati kemudahan akses aplikasi Master Koi dari berbagai perangkat, baik melalui mobile maupun web browser.',
      // Page 3
      'security_title': 'Anti BNR & Rekening Penjamin',
      'security_short': 'Transaksi Aman',
      'security_long': 'Sistem anti Bid and Run dengan rekening penjamin untuk memastikan keamanan setiap transaksi yang Anda lakukan.',
    },
  };

  String get selectLanguage => _localizedValues[locale.languageCode]!['select_language']!;
  String get continueText => _localizedValues[locale.languageCode]!['continue']!;
  String get english => _localizedValues[locale.languageCode]!['english']!;
  String get indonesian => _localizedValues[locale.languageCode]!['indonesian']!;
  String get chooseLanguage => _localizedValues[locale.languageCode]!['choose_language']!;
  String get canChangeLater => _localizedValues[locale.languageCode]!['can_change_later']!;
  String get skip => _localizedValues[locale.languageCode]!['skip']!;
  String get next => _localizedValues[locale.languageCode]!['next']!;
  String get getStarted => _localizedValues[locale.languageCode]!['get_started']!;
  String get bestFishTitle => _localizedValues[locale.languageCode]!['best_fish_title']!;
  String get bestFishShort => _localizedValues[locale.languageCode]!['best_fish_short']!;
  String get bestFishLong => _localizedValues[locale.languageCode]!['best_fish_long']!;
  String get multiPlatformTitle => _localizedValues[locale.languageCode]!['multi_platform_title']!;
  String get multiPlatformShort => _localizedValues[locale.languageCode]!['multi_platform_short']!;
  String get multiPlatformLong => _localizedValues[locale.languageCode]!['multi_platform_long']!;
  String get securityTitle => _localizedValues[locale.languageCode]!['security_title']!;
  String get securityShort => _localizedValues[locale.languageCode]!['security_short']!;
  String get securityLong => _localizedValues[locale.languageCode]!['security_long']!;
}
