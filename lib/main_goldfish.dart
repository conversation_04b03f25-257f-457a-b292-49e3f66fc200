import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:masterkoi_app/config/firebase_config.dart';
import 'main.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorConfig(
    flavor: Flavor.goldfish,
    name: 'Gold Fish',
    appId: 'com.goldfishop.bid',
    appStoreId: '6639588171',
    appName: 'Gold Fish',
    baseUrl: 'https://goldfish.bid',
    privacyPolicyUrl: 'https://goldfish.bid/privacy-policy',
    appStoreUrl: 'https://apps.apple.com/id/app/goldfishop/id6639588171',
    logoPath: 'assets/images/logo_goldfish.png',
    primaryColor: '#FA0707', // Merah
    accentColor: '#66BB6A', // Hijau
  );

  // Initialize Firebase
  await FirebaseConfig.initializeFirebase();

  // Get stored preferences
  final prefs = await SharedPreferences.getInstance();
  final String languageCode = prefs.getString('language_code') ?? 'en';
  final bool hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  // Run the app
  runApp(MasterKoiApp(
    initialLocale: Locale(languageCode),
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}
