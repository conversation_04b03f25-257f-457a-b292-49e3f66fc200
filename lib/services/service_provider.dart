import 'package:masterkoi_app/services/api_service.dart';

class ServiceProvider {
  static final ServiceProvider _instance = ServiceProvider._internal();
  factory ServiceProvider() => _instance;
  ServiceProvider._internal();

  late final ApiService _apiService;

  /*
  void initialize({required String baseUrl}) {
    _apiService = ApiService(baseUrl: baseUrl);
  }

  ApiService get apiService => _apiService;

  void setToken(String token) {
    _apiService.setToken(token);
  }

  void clearToken() {
    _apiService.clearToken();
  }

   */
}
