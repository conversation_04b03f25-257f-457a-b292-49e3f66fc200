import '../models/ikan_lelang.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class IkanLelangService {
  final ApiService _apiService = ApiService();

  /// Get list of ikan lelang with optional filters
  Future<List<IkanLelang>> getIkanLelangList({
    int page = 1,
    String search = '',
    String sort = '',
    Map<String, dynamic>? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'per_page': '10',
      };

      if (search.isNotEmpty) {
        queryParams['search'] = search;
      }

      // Add sort parameter matching Swift implementation
      if (sort.isNotEmpty) {
        queryParams['sort'] = sort;
      }

      // Add filter parameters matching Swift implementation structure
      if (filters != null) {
        // Handle range_last_value_bid filter
        if (filters['range_last_value_bid'] != null) {
          queryParams['range_last_value_bid'] = filters['range_last_value_bid'];
        }

        // Handle rating filter
        if (filters['rating'] != null) {
          queryParams['rating'] = filters['rating'];
        }

        // Handle level filter
        if (filters['level'] != null) {
          queryParams['level'] = filters['level'];
        }

        // Handle priority filter
        if (filters['priority'] != null) {
          queryParams['priority'] = filters['priority'];
        }

        // Handle mode_lelang filter
        if (filters['mode_lelang'] != null) {
          queryParams['mode_lelang'] = filters['mode_lelang'];
        }

        // Handle gender filter
        if (filters['gender'] != null) {
          queryParams['gender'] = filters['gender'];
        }

        // Handle status_bid filter
        if (filters['status_bid'] != null) {
          queryParams['status_bid'] = filters['status_bid'];
        }

        // Handle menjelang_close filter
        if (filters['menjelang_close'] != null) {
          queryParams['menjelang_close'] = filters['menjelang_close'];
        }

        // Handle multiple selection filters (arrays)
        if (filters['selectedIDs_level_seller'] != null && filters['selectedIDs_level_seller'].isNotEmpty) {
          queryParams['selectedIDs_level_seller'] = (filters['selectedIDs_level_seller'] as List).join(',');
        }

        if (filters['selectedIDs_lokasi_ikan'] != null && filters['selectedIDs_lokasi_ikan'].isNotEmpty) {
          queryParams['selectedIDs_lokasi_ikan'] = (filters['selectedIDs_lokasi_ikan'] as List).join(',');
        }

        if (filters['selectedIDs_size_ikan'] != null && filters['selectedIDs_size_ikan'].isNotEmpty) {
          queryParams['selectedIDs_size_ikan'] = (filters['selectedIDs_size_ikan'] as List).join(',');
        }
      }

      final response = await _apiService.get(
        AppConstants.ikanLelangEndpoint,
        queryParameters: queryParams.map((key, value) => MapEntry(key, value.toString())),
      );

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((item) => IkanLelang.fromJson(item)).toList();
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load ikan lelang');
      }
    } catch (e) {
      throw Exception('Error getting ikan lelang list: $e');
    }
  }

  /// Get detail of a specific ikan lelang
  Future<IkanLelang?> getIkanLelangDetail(String idObyekLelang) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.ikanDetailEndpoint}/$idObyekLelang',
      );

      if (response['meta']['code'] == 200) {
        return IkanLelang.fromJson(response['data']);
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load ikan detail');
      }
    } catch (e) {
      throw Exception('Error getting ikan detail: $e');
    }
  }

  /// Add ikan to wishlist
  Future<Map<String, dynamic>> addToWishlist(String idObyekLelang) async {
    try {
      final response = await _apiService.post(
        AppConstants.wishlistAddEndpoint,
        body: {'id_obyek_lelang': idObyekLelang},
      );

      return {
        'success': response['meta']['code'] == 200,
        'message': response['meta']['message'] ?? 'Unknown error',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error adding to wishlist: $e',
      };
    }
  }

  /// Remove ikan from wishlist
  Future<Map<String, dynamic>> removeFromWishlist(String idObyekLelang) async {
    try {
      final response = await _apiService.post(
        AppConstants.wishlistRemoveEndpoint,
        body: {'id_obyek_lelang': idObyekLelang},
      );

      return {
        'success': response['meta']['code'] == 200,
        'message': response['meta']['message'] ?? 'Unknown error',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error removing from wishlist: $e',
      };
    }
  }

  /// Bid on an ikan
  Future<Map<String, dynamic>> bidIkan({
    required String idObyekLelang,
    required String command,
    required String pin,
  }) async {
    try {
      final response = await _apiService.post(
        AppConstants.bidIkanEndpoint,
        body: {
          'id_obyek_lelang': idObyekLelang,
          'command': command,
          'pin': pin,
        },
      );

      return response;
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Error bidding ikan: $e',
        }
      };
    }
  }

  /// Get bidder history
  Future<String> getBidderHistori(String idBuyer) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.bidderHistoriEndpoint}/$idBuyer',
      );

      if (response['meta']['code'] == 200) {
        return response['data']['histori'] ?? '';
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  }

  /// Get wishlist items
  Future<List<IkanLelang>> getWishlist({int page = 1}) async {
    try {
      final response = await _apiService.get(
        AppConstants.wishlistEndpoint,
        queryParameters: {
          'page': page.toString(),
          'per_page': '10',
        },
      );

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((item) => IkanLelang.fromJson(item)).toList();
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load wishlist');
      }
    } catch (e) {
      throw Exception('Error getting wishlist: $e');
    }
  }

  /// Get filter options for ikan lelang
  Future<Map<String, dynamic>> getFilterOptions() async {
    try {
      final response = await _apiService.get(
        AppConstants.ikanLiveStatistikEndpoint,
      );

      if (response['meta']['code'] == 200) {
        return response['data'] ?? {};
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load filter options');
      }
    } catch (e) {
      throw Exception('Error getting filter options: $e');
    }
  }
}