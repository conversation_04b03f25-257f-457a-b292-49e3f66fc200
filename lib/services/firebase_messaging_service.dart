import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/auth_provider.dart';
import '../config/flavor_config.dart';

/// Firebase Messaging Service that matches Swift MasterKoiBot implementation
class FirebaseMessagingService {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  FirebaseMessaging? _messaging;
  AuthProvider? _authProvider;

  /// Initialize Firebase Messaging like Swift AppDelegate
  Future<void> initialize(AuthProvider authProvider) async {
    _authProvider = authProvider;
    _messaging = FirebaseMessaging.instance;

    // Request permission for notifications (like Swift authOptions)
    await _requestPermission();

    // Get initial token
    await _getToken();

    // Listen for token refresh (like Swift didReceiveRegistrationToken)
    FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Check if app was opened from notification
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  /// Request notification permissions like Swift
  Future<void> _requestPermission() async {
    final settings = await _messaging!.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    debugPrint('Notification permission status: ${settings.authorizationStatus}');
  }

  /// Get FCM token and save it like Swift
  Future<void> _getToken() async {
    try {
      final token = await _messaging!.getToken();
      if (token != null) {
        await _saveTokenLocally(token);
        await _sendTokenToServer(token);
      }
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  /// Handle token refresh like Swift didReceiveRegistrationToken
  Future<void> _onTokenRefresh(String token) async {
    debugPrint('Firebase registration token: $token');

    // Save token locally like Swift savePreference
    await _saveTokenLocally(token);

    // Send to server if user is logged in (like Swift session check)
    final prefs = await SharedPreferences.getInstance();
    final session = prefs.getString('session') ?? '';

    if (session.length > 4) {
      await _sendTokenToServer(token);
    }
  }

  /// Save token locally like Swift savePreference
  Future<void> _saveTokenLocally(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcmToken', token);
      debugPrint('FCM token saved locally');
    } catch (e) {
      debugPrint('Error saving FCM token locally: $e');
    }
  }

  /// Send token to server like Swift simpanFCM()
  Future<void> _sendTokenToServer(String token) async {
    if (_authProvider == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';

      if (session.length > 4) {
        // Use exact same endpoint and parameter as Swift
        final response = await _authProvider!.postDirect('/api/profil/set', {
          'token_fcm': token, // Same parameter name as Swift
        });

        final meta = response['meta'];
        final kodeAlert = meta['code'] ?? 0;

        if (meta['action'] == 'login') {
          // Session expired, handle re-login like Swift
          debugPrint('Session expired while saving FCM token');
        } else if (kodeAlert == 200 || kodeAlert == 406) {
          debugPrint('FCM token sent to server successfully: ${meta['message']}');
        } else {
          debugPrint('Failed to send FCM token to server: ${meta['message']}');
        }
      }
    } catch (e) {
      debugPrint('Error sending FCM token to server: $e');
    }
  }

  /// Handle foreground messages like Swift willPresent
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Received foreground message: ${message.messageId}');
    debugPrint('Message data: ${message.data}');

    if (message.notification != null) {
      debugPrint('Message notification: ${message.notification!.title}');
      debugPrint('Message body: ${message.notification!.body}');
    }

    // Handle specific data like Swift id_obyek_lelang
    if (message.data.containsKey('id_obyek_lelang')) {
      final idObyekLelang = message.data['id_obyek_lelang'];
      debugPrint('Received auction notification for: $idObyekLelang');
      // Handle auction-specific logic here
    }
  }

  /// Handle notification tap like Swift didReceive response
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.messageId}');
    debugPrint('Message data: ${message.data}');

    // Handle navigation based on notification data
    if (message.data.containsKey('id_obyek_lelang')) {
      final idObyekLelang = message.data['id_obyek_lelang'];
      debugPrint('Navigate to auction: $idObyekLelang');
      // Navigate to auction detail screen
    }
  }

  /// Get current FCM token
  Future<String?> getCurrentToken() async {
    try {
      return await _messaging?.getToken();
    } catch (e) {
      debugPrint('Error getting current FCM token: $e');
      return null;
    }
  }

  /// Delete FCM token (for logout)
  Future<void> deleteToken() async {
    try {
      await _messaging?.deleteToken();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('fcmToken');
      debugPrint('FCM token deleted');
    } catch (e) {
      debugPrint('Error deleting FCM token: $e');
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging?.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging?.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topic: $e');
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  debugPrint('Handling background message: ${message.messageId}');
  debugPrint('Message data: ${message.data}');

  // Handle background message logic here
  if (message.data.containsKey('id_obyek_lelang')) {
    final idObyekLelang = message.data['id_obyek_lelang'];
    debugPrint('Background auction notification for: $idObyekLelang');
  }
}
