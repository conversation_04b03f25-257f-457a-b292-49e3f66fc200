import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/flavor_config.dart';
import '../utils/constants.dart';
import '../models/rekpen.dart';
import '../models/rekpen_status.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  Future<Map<String, dynamic>> get(String endpoint, {Map<String, String>? queryParameters}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';
      var uri = Uri.parse(FlavorConfig.instance.baseUrl + endpoint);

      if (queryParameters != null) {
        uri = uri.replace(queryParameters: queryParameters);
      }

      final response = await http.get(
        uri,
        headers: {'Session': session},
      );

      final jsonData = json.decode(response.body);
      
      // Check if session expired (similar to MasterKoiBot implementation)
      if (jsonData['meta'] != null && jsonData['meta']['action'] == 'login') {
        return {
          'meta': {
            'code': 401,
            'message': jsonData['meta']['message'] ?? 'Sesi telah berakhir',
            'action': 'login'
          }
        };
      }

      return jsonData;
    } catch (e, stackTrace) {
      // Log error to Sentry
      //await Sentry.captureException(e, stackTrace: stackTrace);
      
      // Return standardized error response
      return {
        'meta': {
          'code': 500,
          'message': 'Gagal melakukan koneksi ke server: $e'
        }
      };
    }
  }

  Future<Map<String, dynamic>> post(String endpoint, {Map<String, dynamic>? body}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';

      final response = await http.post(
        Uri.parse(FlavorConfig.instance.baseUrl + endpoint),
        headers: {
          'Session': session,
          'Content-Type': 'application/json',
        },
        body: json.encode(body),
      );

      final jsonData = json.decode(response.body);
      
      // Check if session expired (similar to MasterKoiBot implementation)
      if (jsonData['meta'] != null && jsonData['meta']['action'] == 'login') {
        return {
          'meta': {
            'code': 401,
            'message': jsonData['meta']['message'] ?? 'Sesi telah berakhir',
            'action': 'login'
          }
        };
      }

      return jsonData;
    } catch (e, stackTrace) {
      // Return standardized error response
      return {
        'meta': {
          'code': 500,
          'message': 'Gagal melakukan koneksi ke server: $e'
        }
      };
    }
  }

  Future<List<RekpenStatus>> getRekpenStatus() async {
    try {
      final response = await get(AppConstants.rekpenStatusEndpoint);

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((json) => RekpenStatus.fromJson(json)).toList();
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan status rekpen: $e';
    }
  }

  // Points specific API call
  Future<Map<String, dynamic>> getPoints() async {
    try {
      final response = await get(AppConstants.pointsEndpoint);
      if (response['meta']['code'] == 200) {
        return {
          'statusCode': 200,
          'data': response['data']
        };
      } else {
        return {
          'statusCode': response['meta']['code'],
          'message': response['meta']['message']
        };
      }
    } catch (e) {
      return {
        'statusCode': 500,
        'message': 'Network error: $e'
      };
    }
  }

  // Profile specific API call
  Future<Map<String, dynamic>> getBidderProfile() async {
    return get(AppConstants.profileEndpoint);
  }

  // Update bidder profile
  Future<Map<String, dynamic>> updateBidderProfile(Map<String, dynamic> data) async {
    return post(AppConstants.bidderSetEndpoint, body: data);
  }

  // Get bidder history
  // Mendapatkan daftar rekpen
  Future<List<Rekpen>> getRekpenList({
    required String idStatusRekpen,
    String search = '',
    int page = 1,
  }) async {
    try {
      final response = await get(
        '${AppConstants.rekpenAllEndpoint}?id_status_payment=$idStatusRekpen&search=$search&page=$page',
      );

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((json) => Rekpen.fromJson(json)).toList();
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan daftar rekpen: $e';
    }
  }

  // Mendapatkan detail rekpen
  Future<Rekpen> getRekpenDetail(String idRekpen) async {
    try {
      final response = await get('${AppConstants.rekpenDetailEndpoint}/$idRekpen');

      if (response['meta']['code'] == 200) {
        return Rekpen.fromJson(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan detail rekpen: $e';
    }
  }

  // Membuat rekpen baru
  Future<void> createRekpen({
    required String noHpPenerima,
    required int nilaiRekpen,
    required String keterangan,
    required String pin,
  }) async {
    try {
      final response = await post(
        AppConstants.rekpenKirimEndpoint,
        body: {
          'no_hp_penerima': noHpPenerima,
          'nilai_rekpen': nilaiRekpen,
          'keterangan': keterangan,
          'pin': pin,
        },
      );

      if (response['meta']['code'] != 200) {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal membuat rekpen: $e';
    }
  }

  // Mendapatkan daftar bank
  Future<List<Map<String, dynamic>>> getBankList() async {
    try {
      final response = await get('/api/bank/list');

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan daftar bank: $e';
    }
  }

  // Melakukan aksi pada rekpen (terima/tolak/bayar)
  Future<bool> rekpenAction({
    required String action,
    required String idRekpen,
    String pin = '',
  }) async {
    try {
      String endpoint;
      Map<String, dynamic> body = {'id_rekpen': idRekpen};

      switch (action.toLowerCase()) {
        case 'terima':
          endpoint = AppConstants.rekpenTerimaEndpoint;
          break;
        case 'tolak':
          endpoint = '${AppConstants.rekpenDetailEndpoint}/tolak';
          break;
        case 'bayar':
          endpoint = AppConstants.rekpenBayarEndpoint;
          if (pin.isNotEmpty) {
            body['pin'] = pin;
          }
          break;
        default:
          throw 'Aksi tidak valid';
      }

      final response = await post(endpoint, body: body);

      if (response['meta']['code'] == 200) {
        return true;
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal melakukan aksi rekpen: $e';
    }
  }

  // Update rekening penerima
  Future<bool> updateRekeningPenerima({
    required String idRekpen,
    required String kodeBank,
    required String nomorRekening,
    required String atasNama,
    required String pin,
  }) async {
    try {
      final response = await post(
        '${FlavorConfig.instance.baseUrl}/update-rekening',
        body: {
          'id_rekpen': idRekpen,
          'kode_bank': kodeBank,
          'nomor_rekening': nomorRekening,
          'atas_nama': atasNama,
          'pin': pin,
        },
      );

      if (response['meta']['code'] == 200) {
        return true;
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mengupdate rekening penerima: $e';
    }
  }

  // Bidder Dashboard
  Future<Map<String, dynamic>> getBidderDashboard() async {
    try {
      final response = await get(AppConstants.bidderDashboardEndpoint);
      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan dashboard bidder: $e';
    }
  }

  // Bidder History
  Future<List<Map<String, dynamic>>> getBidderHistory({
    required int page,
    String search = '',
  }) async {
    try {
      final response = await get(
        '${AppConstants.bidderHistoryEndpoint}?page=$page&search=$search',
      );

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan riwayat bid: $e';
    }
  }

  // Bidder Wishlist
  Future<List<Map<String, dynamic>>> getWishlist({
    required List<String> wishlistIds,
    required int page,
  }) async {
    try {
      final idsString = wishlistIds.join(',');
      final response = await get(
        '/api/ikan/check-live/$idsString?page=$page',
      );

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan wishlist: $e';
    }
  }

  // Toggle Wishlist
  Future<bool> toggleWishlist(String idIkan) async {
    try {
      final response = await post(
        AppConstants.ikanFavoriteEndpoint,
        body: {'id_obyek_lelang': idIkan},
      );

      if (response['meta']['code'] == 200) {
        return true;
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mengubah status wishlist: $e';
    }
  }

  // Get Bidder Points History
  Future<Map<String, dynamic>> getBidderPointsHistory({
    required int page,
    String search = '',
  }) async {
    try {
      final response = await get(
        '/api/poin/history?page=$page&search=$search',
      );

      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan riwayat poin: $e';
    }
  }

  // Get Monthly Draw
  Future<Map<String, dynamic>> getMonthlyDraw() async {
    try {
      final response = await get('/api/undian/info');

      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan info undian: $e';
    }
  }


  // Get My Fish List (Ikan Saya)
  Future<List<Map<String, dynamic>>> getMyFishList({
    required int page,
    String search = '',
  }) async {
    try {
      final response = await get(
        '/api/ikan/my-fish?page=$page&search=$search',
      );

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan daftar ikan: $e';
    }
  }

  // Get My Bids (Bid Saya)
  Future<List<Map<String, dynamic>>> getMyBids({
    required int page,
    String search = '',
  }) async {
    try {
      final response = await get(
        '/api/bid/my-bids?page=$page&search=$search',
      );

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan daftar bid: $e';
    }
  }
  
  // Submit bid on fish
  Future<Map<String, dynamic>> submitBid(String idObyekLelang, String command, String pin) async {
    try {
      final response = await post(
        '/api/ikan/$idObyekLelang/bid',
        body: {
          'id_platform': 4,
          'confirm-pin': pin,
          'command': command
        },
      );
      
      return response;
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Gagal melakukan bid: $e'
        }
      };
    }
  }
  
  // Get fish statistics
  Future<Map<String, dynamic>> getFishStatistics() async {
    try {
      final response = await get('/api/ikan/live/statistik');
      
      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan statistik ikan: $e';
    }
  }
  
  // Get fish details
  Future<Map<String, dynamic>> getFishDetail(String idObyekLelang) async {
    try {
      final response = await get('/api/ikan/$idObyekLelang');
      
      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan detail ikan: $e';
    }
  }
  
  // Set fish rating
  Future<Map<String, dynamic>> setFishRating(
    String idObyekLelang, 
    double ratingKomunikatif,
    double ratingShipment,
    double ratingSpesifikasi,
    double ratingHarga,
    String ulasan
  ) async {
    try {
      final response = await post(
        '/api/ikan/$idObyekLelang/set-rating',
        body: {
          'rating_komunikatif': ratingKomunikatif,
          'rating_shipment': ratingShipment,
          'rating_spesifikasi': ratingSpesifikasi,
          'rating_harga': ratingHarga,
          'ulasan_ikan': ulasan
        },
      );
      
      return response;
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Gagal menyimpan rating: $e'
        }
      };
    }
  }
  
  // Get opsi deal fish
  Future<List<Map<String, dynamic>>> getOpsiDealFish({
    required int page,
    String search = '',
    String rating = '',
    String rangeLastValueBid = '',
    String level = '',
    String gender = '',
    String statusBid = '',
    String menjelangClose = '',
    String kotaSeller = '',
  }) async {
    try {
      final response = await get(
        '/api/ikan/opsi-deal?search=$search&page=$page&min_rating_seller=$rating&range_last_value_bid=$rangeLastValueBid&id_point_level=$level&gender=$gender&is_bidded=$statusBid&will_close_in_minutes=$menjelangClose&kota_seller=$kotaSeller',
      );

      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan daftar ikan opsi deal: $e';
    }
  }
  
  // Get seller dashboard
  Future<Map<String, dynamic>> getSellerDashboard() async {
    try {
      final response = await get(AppConstants.sellerDashboardEndpoint);
      
      if (response['meta']['code'] == 200) {
        return response['data'];
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan dashboard seller: $e';
    }
  }
  
  // Get seller group
  Future<List<Map<String, dynamic>>> getSellerGroup() async {
    try {
      final response = await get('/api/seller/group');
      
      if (response['meta']['code'] == 200) {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw response['meta']['message'];
      }
    } catch (e) {
      throw 'Gagal mendapatkan grup seller: $e';
    }
  }
  
  // Save FCM token
  Future<Map<String, dynamic>> saveFcmToken(String token) async {
    try {
      final response = await post(
        '/api/profil/save-fcm',
        body: {'fcm_token': token},
      );
      
      if (response['meta']['code'] == 200) {
        // Save token locally
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', token);
        return {'success': true, 'message': response['meta']['message']};
      } else {
        return {'success': false, 'message': response['meta']['message']};
      }
    } catch (e) {
      return {'success': false, 'message': 'Gagal menyimpan token FCM: $e'};
    }
  }
}
