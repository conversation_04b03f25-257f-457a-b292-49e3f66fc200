import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:masterkoi_app/screens/utama_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'utils/theme.dart';
import 'utils/app_localizations_delegate.dart';
import 'providers/rekpen_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/wishlist_provider.dart';

import 'screens/onboarding/onboarding_screen.dart';
import 'screens/language/language_selection_screen.dart';
import 'providers/theme_provider.dart';
import 'config/firebase_config.dart';
import 'config/flavor_config.dart';
import 'services/firebase_messaging_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration first
  _initializeFlavor();

  // Initialize Firebase like Swift AppDelegate
  await FirebaseConfig.initializeFirebase();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Check if user has seen onboarding
  final prefs = await SharedPreferences.getInstance();
  final hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  // Get initial locale
  final String? languageCode = prefs.getString('languageCode');
  final Locale initialLocale = languageCode != null ? Locale(languageCode) : Locale('');

  runApp(MasterKoiApp(
    initialLocale: initialLocale,
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}

/// Initialize flavor configuration based on build configuration
void _initializeFlavor() {
  // Default to koifish flavor - this should be configured per build variant
  FlavorConfig(
    flavor: Flavor.koifish,
    name: 'Master Koi Bot',
    appId: 'com.masterkoi.bid',
    appStoreId: '6450153338',
    appName: 'Master Koi',
    baseUrl: 'https://masterkoi.bid',
    privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
    appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id6450153338',
    logoPath: 'assets/images/logo_koifish.png',
    primaryColor: '#11254E', // Dark blue
    accentColor: '#66BB6A', // Green
  );
}

class MasterKoiApp extends StatefulWidget {
  final Locale initialLocale;
  final bool hasSeenOnboarding;

  const MasterKoiApp({
    super.key,
    required this.initialLocale,
    required this.hasSeenOnboarding,
  });

  @override
  State<MasterKoiApp> createState() => _MasterKoiAppState();
}

class _MasterKoiAppState extends State<MasterKoiApp> {
  @override
  void initState() {
    super.initState();
    // Initialize Firebase Messaging after providers are available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeFirebaseMessaging();
    });
  }

  /// Initialize Firebase Messaging like Swift AppDelegate
  Future<void> _initializeFirebaseMessaging() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await FirebaseMessagingService().initialize(authProvider);
    } catch (e) {
      debugPrint('Error initializing Firebase Messaging: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => WishlistProvider()),
        ChangeNotifierProvider(create: (context) => RekpenProvider()),
      ],
      child: MaterialApp(
        title: 'Master Koi App',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        locale: widget.initialLocale,
        supportedLocales: const [
          Locale('id', ''), // Indonesian
          Locale('en', ''), // English
        ],
        localizationsDelegates: const [
          AppLocalizationsDelegate(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: _getInitialScreen(),
      ),
    );
  }

  Widget _getInitialScreen() {
    // Check if language is already selected
    if (!widget.hasSeenOnboarding) {
      return const OnboardingScreen();
    }

    if (widget.initialLocale.languageCode.isEmpty) {
      return const LanguageSelectionScreen();
    }

    return UtamaScreen();
  }
}
