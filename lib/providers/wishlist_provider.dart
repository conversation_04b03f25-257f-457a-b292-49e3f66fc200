import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WishlistProvider extends ChangeNotifier {
  List<String> _wishlist = [];
  static const String _key = 'wishlist';

  List<String> get wishlist => _wishlist;

  WishlistProvider() {
    loadWishlist();
  }

  Future<void> loadWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistData = prefs.getStringList(_key) ?? [];
      _wishlist = wishlistData;
      notifyListeners();
    } catch (e) {
      _wishlist = [];
    }
  }

  Future<void> saveWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_key, _wishlist);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> addToWishlist(String idObyekLelang) async {
    if (!_wishlist.contains(idObyekLelang)) {
      _wishlist.add(idObyekLelang);
      await saveWishlist();
      notifyListeners();
    }
  }

  Future<void> removeFromWishlist(String idObyekLelang) async {
    if (_wishlist.contains(idObyekLelang)) {
      _wishlist.remove(idObyekLelang);
      await saveWishlist();
      notifyListeners();
    }
  }

  bool isBookmarked(String idObyekLelang) {
    return _wishlist.contains(idObyekLelang);
  }

  String getWishlistAsString({String separator = ','}) {
    return _wishlist.join(separator);
  }

  void clearWishlist() async {
    _wishlist.clear();
    await saveWishlist();
    notifyListeners();
  }

  int get wishlistCount => _wishlist.length;
}
