import 'package:flutter/material.dart';
import '../models/rekpen.dart';
import '../models/rekpen_status.dart';
import '../services/api_service.dart';

class RekpenProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // Status
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Data
  List<RekpenStatus> _rekpenStatusList = [];
  List<Rekpen> _rekpenList = [];
  Rekpen? _selectedRekpen;
  RekpenStatus? _selectedStatus;
  
  // Pagination
  int _currentPage = 1;
  bool _hasMoreData = true;
  
  // Search
  String _searchQuery = '';
  
  // Getters
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  List<RekpenStatus> get rekpenStatusList => _rekpenStatusList;
  List<Rekpen> get rekpenList => _rekpenList;
  Rekpen? get selectedRekpen => _selectedRekpen;
  RekpenStatus? get selectedStatus => _selectedStatus;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;
  String get searchQuery => _searchQuery;
  
  // Fungsi untuk mengatur status loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // Fungsi untuk mengatur error
  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }
  
  // Fungsi untuk menghapus error
  void clearError() {
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
  
  // Mendapatkan daftar status rekpen
  Future<void> fetchRekpenStatus() async {
    _setLoading(true);
    clearError();
    
    try {
      final List<RekpenStatus> statusList = await _apiService.getRekpenStatus();
      _rekpenStatusList = statusList;
      
      // Set status default jika belum ada yang dipilih
      if (_selectedStatus == null && _rekpenStatusList.isNotEmpty) {
        _selectedStatus = _rekpenStatusList.first;
        // Fetch rekpen list dengan status default
        await fetchRekpenList(refresh: true);
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  
  // Mendapatkan daftar rekpen
  Future<void> fetchRekpenList({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
    }
    
    if (!_hasMoreData && !refresh) return;
    
    _setLoading(true);
    clearError();
    
    try {
      final idStatusRekpen = _selectedStatus?.idStatusPayment ?? '';
      final rekpenList = await _apiService.getRekpenList(
        idStatusRekpen: idStatusRekpen,
        search: _searchQuery,
        page: _currentPage,
      );
      
      if (refresh) {
        _rekpenList = rekpenList;
      } else {
        _rekpenList.addAll(rekpenList);
      }
      
      _hasMoreData = rekpenList.isNotEmpty;
      if (_hasMoreData) _currentPage++;
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  
  // Mendapatkan detail rekpen
  Future<void> fetchRekpenDetail(String idRekpen) async {
    _setLoading(true);
    clearError();
    
    try {
      final rekpen = await _apiService.getRekpenDetail(idRekpen);
      _selectedRekpen = rekpen;
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
  
  // Membuat rekpen baru
  Future<bool> createRekpen({
    required String noHpPenerima,
    required int nilaiRekpen,
    required String keterangan,
    required String pin,
  }) async {
    _setLoading(true);
    clearError();
    
    try {
      await _apiService.createRekpen(
        noHpPenerima: noHpPenerima,
        nilaiRekpen: nilaiRekpen,
        keterangan: keterangan,
        pin: pin,
      );
      
      // Refresh daftar rekpen
      await fetchRekpenList(refresh: true);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Melakukan aksi pada rekpen
  Future<bool> rekpenAction({
    required String action,
    required String idRekpen,
    String pin = '',
  }) async {
    _setLoading(true);
    clearError();
    
    try {
      final result = await _apiService.rekpenAction(
        action: action,
        idRekpen: idRekpen,
        pin: pin,
      );
      
      if (result) {
        // Refresh detail rekpen jika yang diaksi adalah rekpen yang sedang dipilih
        if (_selectedRekpen != null && _selectedRekpen!.idRekpen == idRekpen) {
          await fetchRekpenDetail(idRekpen);
        }
        
        // Refresh daftar rekpen
        await fetchRekpenList(refresh: true);
      }
      
      return result;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Mengubah status yang dipilih
  void setSelectedStatus(RekpenStatus status) {
    _selectedStatus = status;
    fetchRekpenList(refresh: true);
  }
  
  // Mengubah query pencarian
  void setSearchQuery(String query) {
    _searchQuery = query;
    fetchRekpenList(refresh: true);
  }
  
  // Mendapatkan daftar bank
  Future<List<Map<String, dynamic>>> getBankList() async {
    try {
      return await _apiService.getBankList();
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }
  
  // Update rekening penerima
  Future<bool> updateRekeningPenerima({
    required String idRekpen,
    required String kodeBank,
    required String nomorRekening,
    required String atasNama,
    required String pin,
  }) async {
    _setLoading(true);
    clearError();
    
    try {
      final result = await _apiService.updateRekeningPenerima(
        idRekpen: idRekpen,
        kodeBank: kodeBank,
        nomorRekening: nomorRekening,
        atasNama: atasNama,
        pin: pin,
      );
      
      if (result) {
        // Refresh detail rekpen
        await fetchRekpenDetail(idRekpen);
      }
      
      return result;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }
}
