class PointLevel {
  final int id;
  final int minPoints;
  final int maxPoints;
  final String name;
  final String iconUrl;
  final String description;

  PointLevel({
    required this.id,
    required this.minPoints,
    required this.maxPoints,
    required this.name,
    required this.iconUrl,
    required this.description,
  });

  factory PointLevel.fromJson(Map<String, dynamic> json) {
    return PointLevel(
      id: int.parse(json['id_point_level']?.toString() ?? '0'),
      minPoints: int.parse(json['min_point']?.toString() ?? '0'),
      maxPoints: int.parse(json['max_point']?.toString() ?? '0'),
      name: json['nama_point_level'] ?? '',
      iconUrl: json['icon_url'] ?? '',
      description: json['deskripsi'] ?? '',
    );
  }
}
