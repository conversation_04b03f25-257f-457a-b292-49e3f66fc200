class IkanLelang {
  // Constructor properties
  final String idObyekLelang;
  final String kodeLelang;
  final String namaObyekLelang;
  final String viewFoto;
  final String fotoLelang;
  final String viewVideo;
  final String viewStatusBid;
  final String colorStatusBid;
  final String difavoritkan;
  final String linkObyekLelang;
  final String shortLinkObyekLelang;
  final String idTransaksiBidAsal;
  final String idLelang;
  final String nama;
  final String gender;
  final String ob;
  final String kb;
  final String bin;
  final String viewValueOb;
  final String viewValueKb;
  final String dilihat;
  final String viewDilihat;
  final String lokasiIkan;

  final int valueOb;
  final int valueKb;
  final int valueBin;
  final int lastValueBid;

  final int modePush;

  final int sudahDikirim;
  final int sudahDibayar;

  final int nextKb;
  final String viewNextKb;
  final String jmlBid;
  final String ikanKc;
  final String kodeLelangMode;
  final String namaModeLelang;
  final String visibilityBtnBid;
  final String viewSertifikat;
  final String historiBid;
  final String viewLastValueBid;
  final String keterangan;
  final String closeBidTimer;

  final String colorLastValueBid;
  final int opsiDeal;
  final String lastCloseTimer;
  final int ikanBatal;
  final String noHpMarketing;

  final double ratingKomunikatif;
  final double ratingShipment;
  final double ratingSpesifikasi;
  final double ratingHarga;
  final double ratingIkan;

  // buyer info
  final String idBuyer;
  final String noHpBuyer;
  final String namaBuyer;

  // seller info
  final String namaMerchantSeller;
  final String noHpSeller;
  final String noHpOperator;
  final String listSeller;
  final double ratingSeller;
  final String kotaSeller;
  final String urlMapSeller;
  final int idPointLevel;
  final int asPriority;
  final String namaPointLevel;
  final String iconPointLevel;

  final String textStatsLelang;
  final String textStatsIkanTerlelang;
  final String textStatsAvgNilaiLelang;
  final String ikanTerating;

  final int lunasViaRekpen;
  final String facebook;
  final String instagram;
  final String youtube;
  final String tiktok;
  final String website;


  IkanLelang({
    required this.idObyekLelang,
    required this.kodeLelang,
    required this.namaObyekLelang,
    required this.viewFoto,
    required this.fotoLelang,
    required this.viewVideo,
    required this.viewStatusBid,
    required this.colorStatusBid,
    required this.difavoritkan,
    required this.linkObyekLelang,
    required this.shortLinkObyekLelang,
    required this.idTransaksiBidAsal,
    required this.idLelang,
    required this.viewNextKb,
    required this.nama,
    required this.gender,
    required this.ob,
    required this.kb,
    required this.viewValueOb,
    required this.viewValueKb,
    required this.bin,
    required this.dilihat,
    required this.viewDilihat,
    required this.lokasiIkan,
    required this.valueOb,
    required this.valueKb,
    required this.valueBin,
    required this.lastValueBid,
    required this.modePush,
    required this.sudahDikirim,
    required this.sudahDibayar,
    required this.nextKb,
    required this.jmlBid,
    required this.ikanKc,
    required this.kodeLelangMode,
    required this.namaModeLelang,
    required this.visibilityBtnBid,
    required this.viewSertifikat,
    required this.historiBid,
    required this.viewLastValueBid,
    required this.keterangan,
    required this.closeBidTimer,
    required this.colorLastValueBid,
    required this.opsiDeal,
    required this.lastCloseTimer,
    required this.ikanBatal,
    required this.noHpMarketing,
    required this.ratingKomunikatif,
    required this.ratingShipment,
    required this.ratingSpesifikasi,
    required this.ratingHarga,
    required this.ratingIkan,
    required this.idBuyer,
    required this.noHpBuyer,
    required this.namaBuyer,
    required this.namaMerchantSeller,
    required this.noHpSeller,
    required this.noHpOperator,
    required this.listSeller,
    required this.ratingSeller,
    required this.kotaSeller,
    required this.urlMapSeller,
    required this.idPointLevel,
    required this.asPriority,
    required this.namaPointLevel,
    required this.iconPointLevel,
    required this.textStatsLelang,
    required this.textStatsIkanTerlelang,
    required this.textStatsAvgNilaiLelang,
    required this.ikanTerating,
    required this.lunasViaRekpen,
    required this.facebook,
    required this.instagram,
    required this.youtube,
    required this.tiktok,
    required this.website,
  });

  factory IkanLelang.fromJson(Map<String, dynamic> json) {
    return IkanLelang(
      idObyekLelang: json['id_obyek_lelang'] ?? '',
      kodeLelang: json['kode_lelang'] ?? '',
      namaObyekLelang: json['nama_obyek_lelang'] ?? '',
      viewFoto: json['view_foto'] ?? '',
      fotoLelang: json['foto_lelang'] ?? '',
      viewVideo: json['view_video'] ?? '',
      viewStatusBid: json['view_status_bid'] ?? '',
      colorStatusBid: json['color_status_bid'] ?? '',
      difavoritkan: json['difavoritkan'] ?? '',
      linkObyekLelang: json['link_obyek_lelang'] ?? '',
      shortLinkObyekLelang: json['short_link_obyek_lelang'] ?? '',
      idTransaksiBidAsal: json['id_transaksi_bid_asal'] ?? '',
      idLelang: json['id_lelang'] ?? '',
      viewNextKb: json['view_next_kb'] ?? '',
      nama: json['nama'] ?? '',
      gender: json['gender'] ?? '',
      ob: json['ob'] ?? '',
      kb: json['kb'] ?? '',
      viewValueOb: json['view_value_ob'] ?? '',
      viewValueKb: json['view_value_kb'] ?? '',
      bin: json['bin'] ?? '',
      dilihat: json['dilihat']?.toString() ?? '',
      viewDilihat: json['view_dilihat']?.toString() ?? '',
      lokasiIkan: json['lokasi_ikan']?.toString() ?? '',
      valueOb: json['value_ob'] is int ? json['value_ob'] : int.tryParse(json['value_ob']?.toString() ?? '0') ?? 0,
      valueKb: json['value_kb'] is int ? json['value_kb'] : int.tryParse(json['value_kb']?.toString() ?? '0') ?? 0,
      valueBin: json['value_bin'] is int ? json['value_bin'] : int.tryParse(json['value_bin']?.toString() ?? '0') ?? 0,
      lastValueBid: json['last_value_bid'] is int ? json['last_value_bid'] : int.tryParse(json['last_value_bid']?.toString() ?? '0') ?? 0,
      modePush: json['mode_push'] is int ? json['mode_push'] : int.tryParse(json['mode_push']?.toString() ?? '0') ?? 0,
      sudahDikirim: json['sudah_dikirim'] is int ? json['sudah_dikirim'] : int.tryParse(json['sudah_dikirim']?.toString() ?? '0') ?? 0,
      sudahDibayar: json['sudah_dibayar'] is int ? json['sudah_dibayar'] : int.tryParse(json['sudah_dibayar']?.toString() ?? '0') ?? 0,
      nextKb: json['next_kb'] is int ? json['next_kb'] : int.tryParse(json['next_kb']?.toString() ?? '0') ?? 0,
      jmlBid: json['jml_bid']?.toString() ?? '',
      ikanKc: json['ikan_kc']?.toString() ?? '',
      kodeLelangMode: json['kode_mode_lelang']?.toString() ?? '',
      namaModeLelang: json['nama_mode_lelang']?.toString() ?? '',
      visibilityBtnBid: json['visibility_btn_bid']?.toString() ?? '',
      viewSertifikat: json['view_sertifikat']?.toString() ?? '',
      historiBid: json['histori_bid']?.toString() ?? '',
      viewLastValueBid: json['view_last_value_bid']?.toString() ?? '',
      keterangan: json['keterangan']?.toString() ?? '',
      closeBidTimer: json['close_bid_timer']?.toString() ?? '',
      colorLastValueBid: json['color_last_value_bid']?.toString() ?? '',
      opsiDeal: json['opsi_deal'] is int ? json['opsi_deal'] : int.tryParse(json['opsi_deal']?.toString() ?? '0') ?? 0,
      lastCloseTimer: json['last_close_timer']?.toString() ?? '',
      ikanBatal: json['ikan_batal'] is int ? json['ikan_batal'] : int.tryParse(json['ikan_batal']?.toString() ?? '0') ?? 0,
      noHpMarketing: json['ref_marketing']?['no_hp_marketing']?.toString() ?? '',
      ratingKomunikatif: (json['rating_komunikatif'] ?? 0.0).toDouble(),
      ratingShipment: (json['rating_shipment'] ?? 0.0).toDouble(),
      ratingSpesifikasi: (json['rating_spesifikasi'] ?? 0.0).toDouble(),
      ratingHarga: (json['rating_harga'] ?? 0.0).toDouble(),
      ratingIkan: double.tryParse(json['rating_ikan']?.toString() ?? '0.0') ?? 0.0,
      idBuyer: json['ref_winner']?['id_buyer']?.toString() ?? '',
      noHpBuyer: json['ref_winner']?['no_hp_buyer']?.toString() ?? '',
      namaBuyer: json['ref_winner']?['nama_buyer']?.toString() ?? '',
      namaMerchantSeller: json['ref_seller']?['nama_merchant_seller']?.toString() ?? '',
      noHpSeller: json['ref_seller']?['no_hp_seller']?.toString() ?? '',
      noHpOperator: json['ref_seller']?['no_hp_operator']?.toString() ?? '',
      listSeller: json['ref_seller']?['list_seller']?.toString() ?? '',
      ratingSeller: double.tryParse(json['ref_seller']?['rating_seller']?.toString() ?? '0.0') ?? 0.0,
      kotaSeller: json['ref_seller']?['kota_seller']?.toString() ?? '',
      urlMapSeller: json['ref_seller']?['url_map_seller']?.toString() ?? '',
      idPointLevel: int.tryParse(json['ref_seller']?['id_point_level']?.toString() ?? '0') ?? 0,
      asPriority: int.tryParse(json['ref_seller']?['as_priority']?.toString() ?? '0') ?? 0,
      namaPointLevel: json['ref_seller']?['nama_point_level']?.toString() ?? '',
      iconPointLevel: json['ref_seller']?['icon_point_level']?.toString() ?? '',
      textStatsLelang: json['ref_seller']?['text_stats_lelang']?.toString() ?? '',
      textStatsIkanTerlelang: json['ref_seller']?['text_stats_ikan_terlelang']?.toString() ?? '',
      textStatsAvgNilaiLelang: json['ref_seller']?['text_stats_avg_nilai_lelang']?.toString() ?? '',
      ikanTerating: json['ref_seller']?['ikan_terating']?.toString() ?? '',
      lunasViaRekpen: json['lunas_via_rekpen'] is int ? json['lunas_via_rekpen'] : int.tryParse(json['lunas_via_rekpen']?.toString() ?? '0') ?? 0,
      facebook: json['ref_seller']?['facebook'] ?? '',
      instagram: json['ref_seller']?['instagram'] ?? '',
      youtube: json['ref_seller']?['youtube'] ?? '',
      tiktok: json['ref_seller']?['tiktok'] ?? '',
      website: json['ref_seller']?['website'] ?? ''
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id_obyek_lelang': idObyekLelang,
      'nama_obyek_lelang': namaObyekLelang,
      'view_foto': viewFoto,
      'foto_lelang': fotoLelang,
      'view_video': viewVideo,
      'view_status_bid': viewStatusBid,
      'color_status_bid': colorStatusBid,
      'difavoritkan': difavoritkan,
      'link_obyek_lelang': linkObyekLelang,
      'short_link_obyek_lelang': shortLinkObyekLelang,
      'id_transaksi_bid_asal': idTransaksiBidAsal,
      'id_lelang': idLelang,
      'view_next_kb': viewNextKb,
      'nama': nama,
      'gender': gender,
      'ob': ob,
      'kb': kb,
      'bin': bin,
      'dilihat': dilihat,
      'view_dilihat': viewDilihat,
      'lokasi_ikan': lokasiIkan,
      'value_ob': valueOb,
      'value_kb': valueKb,
      'value_bin': valueBin,
      'last_value_bid': lastValueBid,
      'sudah_dikirim': sudahDikirim,
      'sudah_dibayar': sudahDibayar,
      'next_kb': nextKb,
      'jml_bid': jmlBid,
      'ikan_kc': ikanKc,
      'kode_mode_lelang': kodeLelangMode,
      'nama_mode_lelang': namaModeLelang,
      'visibility_btn_bid': visibilityBtnBid,
      'view_sertifikat': viewSertifikat,
      'histori_bid': historiBid,
      'view_last_value_bid': viewLastValueBid,
      'keteragan': keterangan,
      'close_bid_timer': closeBidTimer,
      'color_last_value_bid': colorLastValueBid,
      'opsi_deal': opsiDeal,
      'last_close_timer': lastCloseTimer,
      'ikan_batal': ikanBatal,
      'ref_marketing': {
        'no_hp_marketing': noHpMarketing,
      },
      'rating_komunikatif': ratingKomunikatif,
      'rating_shipment': ratingShipment,
      'rating_spesifikasi': ratingSpesifikasi,
      'rating_harga': ratingHarga,
      'rating_ikan': ratingIkan,
      'ref_winner': {
        'id_buyer': idBuyer,
        'no_hp_buyer': noHpBuyer,
        'nama_buyer': namaBuyer,
      },
      'ref_seller': {
        'nama_merchant_seller': namaMerchantSeller,
        'no_hp_seller': noHpSeller,
        'no_hp_operator': noHpOperator,
        'list_seller': listSeller,
        'rating_seller': ratingSeller,
        'kota_seller': kotaSeller,
        'url_map_seller': urlMapSeller,
        'id_point_level': idPointLevel,
        'as_priority': asPriority,
        'nama_point_level': namaPointLevel,
        'icon_point_level': iconPointLevel,
        'text_stats_lelang': textStatsLelang,
        'text_stats_ikan_terlelang': textStatsIkanTerlelang,
        'text_stats_avg_nilai_lelang': textStatsAvgNilaiLelang,
        'ikan_terating': ikanTerating,
        'facebook': facebook,
        'instagram': instagram,
        'youtube': youtube,
        'tiktok': tiktok,
        'website': website,
      },
      'lunas_via_rekpen': lunasViaRekpen,
    };
  }
}

