class RekpenStatus {
  final String idStatusPayment;
  final String namaStatusPayment;
  final String kodeStatusPayment;
  final String namaStatusPaymentIndonesia;
  final String colorBgStatusPayment;
  final String colorTextStatusPayment;

  RekpenStatus({
    required this.idStatusPayment,
    required this.namaStatusPayment,
    required this.kodeStatusPayment,
    this.namaStatusPaymentIndonesia = '',
    this.colorBgStatusPayment = '#FFFFFF',
    this.colorTextStatusPayment = '#000000',
  });

  factory RekpenStatus.fromJson(Map<String, dynamic> json) {
    return RekpenStatus(
      idStatusPayment: json['id_status_payment'] ?? '',
      namaStatusPayment: json['nama_status_payment'] ?? '',
      kodeStatusPayment: json['kode_status_payment'] ?? '',
      namaStatusPaymentIndonesia: json['nama_status_payment_indonesia'] ?? '',
      colorBgStatusPayment: json['color_bg_status_payment'] ?? '#FFFFFF',
      colorTextStatusPayment: json['color_text_status_payment'] ?? '#000000',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id_status_payment': idStatusPayment,
      'nama_status_payment': namaStatusPayment,
      'kode_status_payment': kodeStatusPayment,
      'nama_status_payment_indonesia': namaStatusPaymentIndonesia,
      'color_bg_status_payment': colorBgStatusPayment,
      'color_text_status_payment': colorTextStatusPayment,
    };
  }
}
