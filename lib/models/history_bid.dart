class HistoryBid {
  final String id;
  final String idTransaksiBid;
  final String idBuyer;
  final String idBidGroup;
  final String idObyekLelang;
  final String tglTransaksiBid;
  final String tokenTransaksiBid;
  final String chatTransaksiBid;
  final String listTransaksiBid;
  final String nilaiTransaksiBid;
  final String buyerTransaksiBid;
  final String buyerEnkripsiTransaksiBid;
  final String inputTimer;
  final String viewIkanTransaksiBid;
  final String viewFoto;

  HistoryBid({
    String? id,
    required this.idTransaksiBid,
    required this.idBuyer,
    required this.idBidGroup,
    required this.idObyekLelang,
    required this.tglTransaksiBid,
    required this.tokenTransaksiBid,
    required this.chatTransaksiBid,
    required this.listTransaksiBid,
    required this.nilaiTransaksiBid,
    required this.buyerTransaksiBid,
    required this.buyerEnkripsiTransaksiBid,
    required this.inputTimer,
    required this.viewIkanTransaksiBid,
    required this.viewFoto,
  }) : this.id = id ?? idTransaksiBid;

  factory HistoryBid.fromJson(Map<String, dynamic> json) {
    return HistoryBid(
      idTransaksiBid: json['id_transaksi_bid'] ?? '',
      idBuyer: json['id_buyer'] ?? '',
      idBidGroup: json['id_bid_group'] ?? '',
      idObyekLelang: json['id_obyek_lelang'] ?? '',
      tglTransaksiBid: json['tgl_transaksi_bid'] ?? '',
      tokenTransaksiBid: json['token_transaksi_bid'] ?? '',
      chatTransaksiBid: json['chat_transaksi_bid'] ?? '',
      listTransaksiBid: json['list_transaksi_bid'] ?? '',
      nilaiTransaksiBid: json['nilai_transaksi_bid'] ?? '',
      buyerTransaksiBid: json['buyer_transaksi_bid'] ?? '',
      buyerEnkripsiTransaksiBid: json['buyer_enkripsi_transaksi_bid'] ?? '',
      inputTimer: json['input_timer'] ?? '',
      viewIkanTransaksiBid: json['view_ikan_transaksi_bid'] ?? '',
      viewFoto: json['obyek_lelang']?['view_foto'] ?? '',
    );
  }
}
