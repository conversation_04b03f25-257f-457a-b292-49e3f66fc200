class Rekpen {
  final String idRekpen;
  final String tokenRekpen;
  final String noHpPengirim;
  final String noHpPenerima;
  final String nilaiRekpen;
  final String viewNilaiRekpen;
  final String viewBiayaRekpen;
  final String viewTotalRekpen;
  final String bankForward;
  final String nomorRekeningForward;
  final String atasNamaForward;
  final String keterangan;
  final String statusPayment;
  final String namaStatusPayment;
  final String linkPaymentRekpen;
  final int showEditRekening;
  final int showBayarSekarangPengirim;
  final int showTeruskanDanaPengirim;
  final int showBatalkanPengirim;
  final int showSetujuiPengembalianDana;
  final int showAjukanRefund;
  final int showAjukanPencairanDanaPenerima;
  final int showRekeningSudahSesuai;
  final int showAktifkanRekpenPengirim;
  final String createdAt;
  final String updatedAt;
  final String povStatus;
  final String? kodeBank;
  final String? nomorRekening;
  final String? atasNama;
  final String? bankName;
  final String? buktiTransfer;
  final String colorBgStatusPayment;
  final String colorTextStatusPayment;

  Rekpen({
    required this.idRekpen,
    required this.tokenRekpen,
    required this.noHpPengirim,
    required this.noHpPenerima,
    required this.nilaiRekpen,
    required this.viewNilaiRekpen,
    required this.viewBiayaRekpen,
    required this.viewTotalRekpen,
    required this.bankForward,
    required this.nomorRekeningForward,
    required this.atasNamaForward,
    required this.keterangan,
    required this.statusPayment,
    required this.namaStatusPayment,
    required this.linkPaymentRekpen,
    required this.showEditRekening,
    required this.showBayarSekarangPengirim,
    required this.showTeruskanDanaPengirim,
    required this.showBatalkanPengirim,
    required this.showSetujuiPengembalianDana,
    required this.showAjukanRefund,
    required this.showAjukanPencairanDanaPenerima,
    required this.showRekeningSudahSesuai,
    required this.showAktifkanRekpenPengirim,
    required this.createdAt,
    required this.updatedAt,
    required this.povStatus,
    this.kodeBank,
    this.nomorRekening,
    this.atasNama,
    this.bankName,
    this.buktiTransfer,
    required this.colorBgStatusPayment,
    required this.colorTextStatusPayment,
  });

  factory Rekpen.fromJson(Map<String, dynamic> json) {
    return Rekpen(
      idRekpen: json['id_rekpen'] ?? '',
      tokenRekpen: json['token_rekpen'] ?? '',
      noHpPengirim: json['no_hp_pengirim'] ?? '',
      noHpPenerima: json['no_hp_penerima'] ?? '',
      nilaiRekpen: json['nilai_rekpen'] ?? '',
      viewNilaiRekpen: json['view_nilai_rekpen'] ?? '',
      viewBiayaRekpen: json['view_biaya_rekpen'] ?? '',
      viewTotalRekpen: json['view_total_rekpen'] ?? '',
      bankForward: json['bank_forward'] ?? '',
      nomorRekeningForward: json['nomor_rekening_forward'] ?? '',
      atasNamaForward: json['atas_nama_forward'] ?? '',
      keterangan: json['keterangan'] ?? '',
      statusPayment: json['status_payment'] ?? '',
      namaStatusPayment: json['nama_status_payment'] ?? '',
      linkPaymentRekpen: json['link_payment_rekpen'] ?? '',
      showEditRekening: json['show_edit_rekening'] ?? 0,
      showBayarSekarangPengirim: json['show_bayar_sekarang_pengirim'] ?? 0,
      showTeruskanDanaPengirim: json['show_teruskan_dana_pengirim'] ?? 0,
      showBatalkanPengirim: json['show_batalkan_pengirim'] ?? 0,
      showSetujuiPengembalianDana: json['show_setujui_pengembalian_dana'] ?? 0,
      showAjukanRefund: json['show_ajukan_refund'] ?? 0,
      showAjukanPencairanDanaPenerima: json['show_ajukan_pencairan_dana_penerima'] ?? 0,
      showRekeningSudahSesuai: json['show_rekening_sudah_sesuai'] ?? 0,
      showAktifkanRekpenPengirim: json['show_aktifkan_rekpen_pengirim'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      povStatus: json['pov_status'] ?? '',
      kodeBank: json['kode_bank'],
      nomorRekening: json['nomor_rekening'],
      atasNama: json['atas_nama'],
      bankName: json['bank_name'],
      buktiTransfer: json['bukti_transfer'],
      colorBgStatusPayment: json['color_bg_status_payment'] ?? '#FFFFFF',
      colorTextStatusPayment: json['color_text_status_payment'] ?? '#000000',
    );
  }
}
