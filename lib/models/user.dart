class User {
  final String viewNama;
  final String noWa;
  final String session;
  String? fcmToken;

  // Seller data
  final String? kodeSeller;
  final String? namaMerchantSeller;
  final String? viewUrlFotoLogo;
  final String? listSeller;
  final String? namaPemilikSeller;
  final String? facebook;
  final String? instagram;
  final String? youtube;
  final String? tiktok;
  final String? website;
  final String? ratingSeller;

  // Buyer data
  final String? namaBuyer;
  final String? listBuyer;
  final String? noHpBuyer;
  final String? asBnr;
  final String? nomorBantuan;

  bool get isSeller => kodeSeller != null && kodeSeller!.isNotEmpty;
  bool get isBuyer => namaBuyer != null && namaBuyer!.isNotEmpty;

  User({
    required this.viewNama,
    required this.noWa,
    required this.session,
    this.fcmToken,
    this.kodeSeller,
    this.namaMerchantSeller,
    this.viewUrlFotoLogo,
    this.listSeller,
    this.namaPemilikSeller,
    this.facebook,
    this.instagram,
    this.youtube,
    this.tiktok,
    this.website,
    this.ratingSeller,
    this.namaBuyer,
    this.listBuyer,
    this.noHpBuyer,
    this.asBnr,
    this.nomorBantuan,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      viewNama: json['view_nama'] ?? '',
      noWa: json['no_wa'] ?? '',
      session: json['session'] ?? '',
      fcmToken: json['fcm_token'],
      kodeSeller: json['seller']?['kode_seller'],
      namaMerchantSeller: json['seller']?['nama_merchant_seller'],
      viewUrlFotoLogo: json['seller']?['view_url_foto_logo'],
      listSeller: json['seller']?['list_seller'],
      namaPemilikSeller: json['seller']?['nama_pemilik_seller'],
      facebook: json['seller']?['facebook'],
      instagram: json['seller']?['instagram'],
      youtube: json['seller']?['youtube'],
      tiktok: json['seller']?['tiktok'],
      website: json['seller']?['website'],
      ratingSeller: json['seller']?['rating_seller'],
      namaBuyer: json['buyer']?['nama_buyer'],
      listBuyer: json['buyer']?['list_buyer'],
      noHpBuyer: json['buyer']?['no_hp_buyer'],
      asBnr: json['buyer']?['as_bnr'],
      nomorBantuan: json['buyer']?['nomor_bantuan'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'view_nama': viewNama,
      'no_wa': noWa,
      'session': session,
      'fcm_token': fcmToken,
      'seller': {
        'kode_seller': kodeSeller,
        'nama_merchant_seller': namaMerchantSeller,
        'view_url_foto_logo': viewUrlFotoLogo,
        'list_seller': listSeller,
        'nama_pemilik_seller': namaPemilikSeller,
        'facebook': facebook,
        'instagram': instagram,
        'youtube': youtube,
        'tiktok': tiktok,
        'website': website,
        'rating_seller': ratingSeller,
      },
      'buyer': {
        'nama_buyer': namaBuyer,
        'list_buyer': listBuyer,
        'no_hp_buyer': noHpBuyer,
        'as_bnr': asBnr,
        'nomor_bantuan': nomorBantuan,
      },
    };
  }

  User copyWith({
    String? viewNama,
    String? noWa,
    String? session,
    String? fcmToken,
    String? kodeSeller,
    String? namaMerchantSeller,
    String? viewUrlFotoLogo,
    String? listSeller,
    String? namaPemilikSeller,
    String? facebook,
    String? instagram,
    String? youtube,
    String? tiktok,
    String? website,
    String? ratingSeller,
    String? namaBuyer,
    String? listBuyer,
    String? noHpBuyer,
    String? asBnr,
    String? nomorBantuan,
  }) {
    return User(
      viewNama: viewNama ?? this.viewNama,
      noWa: noWa ?? this.noWa,
      session: session ?? this.session,
      fcmToken: fcmToken ?? this.fcmToken,
      kodeSeller: kodeSeller ?? this.kodeSeller,
      namaMerchantSeller: namaMerchantSeller ?? this.namaMerchantSeller,
      viewUrlFotoLogo: viewUrlFotoLogo ?? this.viewUrlFotoLogo,
      listSeller: listSeller ?? this.listSeller,
      namaPemilikSeller: namaPemilikSeller ?? this.namaPemilikSeller,
      facebook: facebook ?? this.facebook,
      instagram: instagram ?? this.instagram,
      youtube: youtube ?? this.youtube,
      tiktok: tiktok ?? this.tiktok,
      website: website ?? this.website,
      ratingSeller: ratingSeller ?? this.ratingSeller,
      namaBuyer: namaBuyer ?? this.namaBuyer,
      listBuyer: listBuyer ?? this.listBuyer,
      noHpBuyer: noHpBuyer ?? this.noHpBuyer,
      asBnr: asBnr ?? this.asBnr,
      nomorBantuan: nomorBantuan ?? this.nomorBantuan,
    );
  }
}
