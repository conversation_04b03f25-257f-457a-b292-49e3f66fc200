import 'package:masterkoi_app/models/point_level.dart';

class UserPoints {
  final double summaryPointReward;
  final String viewSummaryPointReward;
  final double balancePointReward;
  final String viewBalancePointReward;
  final PointLevel pointLevel;
  final String coupon;
  final String couponWarning;

  UserPoints({
    required this.summaryPointReward,
    required this.viewSummaryPointReward,
    required this.balancePointReward,
    required this.viewBalancePointReward,
    required this.pointLevel,
    required this.coupon,
    required this.couponWarning,
  });

  factory UserPoints.fromJson(Map<String, dynamic> json) {
    return UserPoints(
      summaryPointReward: double.parse(json['summary_point_reward'].toString()),
      viewSummaryPointReward: json['view_summary_point_reward'] ?? '',
      balancePointReward: double.parse(json['balance_point_reward'].toString()),
      viewBalancePointReward: json['view_balance_point_reward'] ?? '',
      pointLevel: PointLevel.fromJson(json['point_level'] ?? {}),
      coupon: json['coupon'] ?? '',
      couponWarning: json['coupon_warning'] ?? '',
    );
  }
}
