import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/ikan_lelang.dart';
import '../../services/api_service.dart';
import '../../widgets/interactive_star_rating.dart';
import '../../utils/colors.dart';

/// IkanRatingScreen - Complete rating system implementation
/// Based on MasterKoiBot IkanRating.swift with enhanced functionality
class IkanRatingScreen extends StatefulWidget {
  final String idObyekLelang;
  final String halaman;
  final String judulHalaman;

  const IkanRatingScreen({
    Key? key,
    required this.idObyekLelang,
    required this.halaman,
    required this.judulHalaman,
  }) : super(key: key);

  @override
  State<IkanRatingScreen> createState() => _IkanRatingScreenState();
}

class _IkanRatingScreenState extends State<IkanRatingScreen> {
  bool _isLoading = true;
  bool _isLoadingSimpan = false;
  bool _needLogin = false;
  IkanLelang? _ikanLelang;
  
  // Rating values
  double _ratingKomunikatif = 0.0;
  double _ratingShipment = 0.0;
  double _ratingSpesifikasi = 0.0;
  double _ratingHarga = 0.0;
  
  // Review text
  final TextEditingController _ulasanController = TextEditingController();
  
  // Editable state
  bool _editable = false;

  @override
  void initState() {
    super.initState();
    _loadIkanData();
  }

  @override
  void dispose() {
    _ulasanController.dispose();
    super.dispose();
  }

  Future<void> _loadIkanData() async {
    try {
      final response = await ApiService().getFishDetail(widget.idObyekLelang);
      
      if (response['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
      } else if (response['meta']['code'] == 200) {
        final data = response['data'];
        setState(() {
          _ikanLelang = IkanLelang.fromJson(data);
          _ratingKomunikatif = data['rating_komunikatif']?.toDouble() ?? 0.0;
          _ratingShipment = data['rating_shipment']?.toDouble() ?? 0.0;
          _ratingSpesifikasi = data['rating_spesifikasi']?.toDouble() ?? 0.0;
          _ratingHarga = data['rating_harga']?.toDouble() ?? 0.0;
          _ulasanController.text = data['ulasan_ikan'] ?? '';
          
          // Check if rating is editable
          if (widget.halaman == "Ikan Saya") {
            _editable = data['editable_rating'] == 1;
          } else {
            _editable = true;
          }
        });
      }
    } catch (e) {
      _showErrorDialog('Error loading data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveRating() async {
    if (!_editable) return;
    
    setState(() => _isLoadingSimpan = true);
    
    try {
      final response = await ApiService().setFishRating(
        widget.idObyekLelang,
        _ratingKomunikatif,
        _ratingShipment,
        _ratingSpesifikasi,
        _ratingHarga,
        _ulasanController.text,
      );
      
      if (response['meta']['code'] == 200) {
        _showSuccessDialog('Rating berhasil disimpan');
      } else {
        _showErrorDialog(response['meta']['message'] ?? 'Gagal menyimpan rating');
      }
    } catch (e) {
      _showErrorDialog('Error saving rating: $e');
    } finally {
      setState(() => _isLoadingSimpan = false);
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sukses'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Return to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.judulHalaman.isEmpty ? 'Rating Ikan' : widget.judulHalaman),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _ikanLelang == null
              ? const Center(child: Text('Data tidak ditemukan'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Fish Info Card
                      _buildFishInfoCard(),
                      const SizedBox(height: 24),
                      
                      // Rating Section
                      _buildRatingSection(),
                      const SizedBox(height: 24),
                      
                      // Review Section
                      _buildReviewSection(),
                      const SizedBox(height: 32),
                      
                      // Save Button
                      if (_editable) _buildSaveButton(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildFishInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _ikanLelang!.namaObyekLelang,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (_ikanLelang!.viewLastValueBid.isNotEmpty)
              Text(
                'Harga: ${_ikanLelang!.viewLastValueBid}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            const SizedBox(height: 8),
            Text(
              'Seller: ${_ikanLelang!.namaMerchantSeller}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Berikan Rating',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        _buildRatingItem(
          'Kemudahan menghubungi seller',
          Icons.message,
          _ratingKomunikatif,
          (rating) => setState(() => _ratingKomunikatif = rating),
        ),
        
        _buildRatingItem(
          'Kualitas Pengiriman',
          Icons.local_shipping,
          _ratingShipment,
          (rating) => setState(() => _ratingShipment = rating),
        ),
        
        _buildRatingItem(
          'Kesesuaian Spesifikasi',
          Icons.check_circle,
          _ratingSpesifikasi,
          (rating) => setState(() => _ratingSpesifikasi = rating),
        ),
        
        _buildRatingItem(
          'Kesesuaian Harga',
          Icons.attach_money,
          _ratingHarga,
          (rating) => setState(() => _ratingHarga = rating),
        ),
      ],
    );
  }

  Widget _buildRatingItem(String title, IconData icon, double rating, Function(double) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: AppColors.primary),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const SizedBox(width: 28),
              InteractiveStarRating(
                initialRating: rating,
                onRatingChanged: onChanged,
                isEditable: _editable,
                size: 32,
              ),
              const SizedBox(width: 8),
              Text(
                rating.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ulasan',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _ulasanController,
          enabled: _editable,
          maxLines: 5,
          decoration: InputDecoration(
            hintText: 'Tulis ulasan Anda tentang ikan ini...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoadingSimpan ? null : _saveRating,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoadingSimpan
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text(
                'Simpan Rating',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
