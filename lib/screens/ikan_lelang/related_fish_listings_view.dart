import 'package:flutter/material.dart';
import '../../models/ikan_lelang.dart';
import '../../services/api_service.dart';
import 'ikan_lelang_lain_grid_item.dart';

/// RelatedFishListingsView - Single file approach
/// Converted from Swift RelatedFishListingsView.swift
/// Shows related fish listings with pagination
class RelatedFishListingsView extends StatefulWidget {
  final String title;
  final String endpoint;
  final String idObyekLelang;
  final String idLelang;

  const RelatedFishListingsView({
    super.key,
    required this.title,
    required this.endpoint,
    required this.idObyekLelang,
    this.idLelang = '',
  });

  @override
  State<RelatedFishListingsView> createState() =>
      _RelatedFishListingsViewState();
}

class _RelatedFishListingsViewState extends State<RelatedFishListingsView> {
  final List<IkanLelang> _fishList = [];
  int _page = 1;
  bool _isLoading = false;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _getFishList();
  }

  Future<void> _getFishList() async {
    if (_isLoading || !_hasMoreData) return;

    setState(() => _isLoading = true);

    try {
      final queryParameters = <String, dynamic>{
        'page': _page.toString(),
        'per_page': '25',
      };

      if (widget.idLelang.isNotEmpty) {
        queryParameters['id_lelang'] = widget.idLelang;
        queryParameters['id_obyek_lelang'] = widget.idObyekLelang;
      }

      final response = await ApiService().get(
        widget.endpoint,  // Removed '/api/' prefix since ApiService should handle it
        queryParameters: queryParameters.map((key, value) => MapEntry(key, value.toString())),
      );

      if (response['meta']['code'] == 200) {
        final items = response['data'] as List;
        final newFish = items.map((item) => IkanLelang.fromJson(item)).toList();

        setState(() {
          if (_page == 1) {
            _fishList.clear();
          }
          _fishList.addAll(newFish);
          _hasMoreData = newFish.isNotEmpty;
          _page++;
        });
      }
    } catch (e) {
      // Handle error silently or show error message
      debugPrint('Error loading related fish: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Future<void> _refreshData() async {
  //   _page = 1;
  //   _hasMoreData = true;
  //   await _getFishList();
  // }

  @override
  Widget build(BuildContext context) {
    if (_fishList.isEmpty && !_isLoading) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            widget.title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),

        // Fish Grid
        if (_fishList.isNotEmpty)
          SizedBox(
            height: 280, // Fixed height for horizontal scrolling
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _fishList.length + (_isLoading ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _fishList.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final fish = _fishList[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: IkanLelangLainGridItem(
                    item: fish,
                    onTap: () {
                      // Navigate to fish detail
                      Navigator.pushNamed(
                        context,
                        '/ikan-detail',
                        arguments: fish.idObyekLelang,
                      );
                    },
                  ),
                );
              },
            ),
          ),

        // Loading indicator for initial load
        if (_fishList.isEmpty && _isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          ),

        // Load More Button
        if (_fishList.isNotEmpty && _hasMoreData && !_isLoading)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: ElevatedButton(
                onPressed: _getFishList,
                child: const Text('Muat Lebih Banyak'),
              ),
            ),
          ),

        const Divider(),
      ],
    );
  }
}

/// RelatedFishViewModel - Data management class
/// Equivalent to RelatedFishViewModel in Swift
class RelatedFishViewModel extends ChangeNotifier {
  final List<IkanLelang> _fishList = [];
  int _page = 1;
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  final String _endpoint;
  final String _idObyekLelang;
  final String _idLelang;

  RelatedFishViewModel({
    required String endpoint,
    required String idObyekLelang,
    String idLelang = '',
  }) : _endpoint = endpoint,
       _idObyekLelang = idObyekLelang,
       _idLelang = idLelang;

  // Getters
  List<IkanLelang> get fishList => _fishList;
  int get page => _page;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;

  // Methods
  Future<void> getFishList() async {
    if (_isLoading) return;

    _setLoading(true);
    _clearError();

    try {
      final queryParameters = <String, dynamic>{
        'page': _page.toString(),
        'per_page': '25',
      };

      if (_idLelang.isNotEmpty) {
        queryParameters['id_lelang'] = _idLelang;
        queryParameters['id_obyek_lelang'] = _idObyekLelang;
      }

      final response = await ApiService().get(
        'api/$_endpoint',  // Adding api/ prefix to match Swift implementation
        queryParameters: queryParameters.map((key, value) => MapEntry(key, value.toString())),
      );

      if (response['meta']['code'] == 200) {
        final items = response['data'] as List;
        final newFish = items.map((item) => IkanLelang.fromJson(item)).toList();

        if (_page == 1) {
          _fishList.clear();
        }
        _fishList.addAll(newFish);
        _page++;

        notifyListeners();
      } else {
        _setError(response['meta']['message'] ?? 'Unknown error');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshData() async {
    _page = 1;
    await getFishList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _hasError = false;
    _errorMessage = '';
  }
}
