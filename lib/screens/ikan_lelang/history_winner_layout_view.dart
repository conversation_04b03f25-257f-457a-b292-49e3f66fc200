import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../models/ikan_lelang.dart';

/// HistoryWinnerLayoutView - Single file approach
/// Converted from Swift HistoryWinnerLayoutView.swift
/// Expandable section showing bid history in HTML format
class HistoryWinnerLayoutView extends StatefulWidget {
  final IkanLelang ikanLelang;

  const HistoryWinnerLayoutView({super.key, required this.ikanLelang});

  @override
  State<HistoryWinnerLayoutView> createState() =>
      _HistoryWinnerLayoutViewState();
}

class _HistoryWinnerLayoutViewState extends State<HistoryWinnerLayoutView> {
  bool _isExpanded = true; // Default to expanded like in Swift

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Row(
                children: [
                  const Icon(Icons.history),
                  const SizedBox(width: 8),
                  const Text(
                    'Histori Lelang',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                  ),
                ],
              ),
            ),

            // Expandable Content
            if (_isExpanded) ...[
              const SizedBox(height: 16),

              // HTML Content
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(left: 16),
                child: _buildHistoryContent(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryContent() {
    // Check if we have actual history data
    final historyData = _getHistoryData();

    if (historyData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: const Center(
          child: Text(
            'Belum ada histori bid untuk ikan ini',
            style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Html(
        data: historyData,
        style: {
          "body": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.all(12),
            fontSize: FontSize(14),
            lineHeight: const LineHeight(1.7),
          ),
          "table": Style(
            width: Width(double.infinity),
            border: Border.all(color: Colors.grey[300]!),
          ),
          "th": Style(
            backgroundColor: Theme.of(
              context,
            ).primaryColor.withValues(alpha: 0.1),
            padding: HtmlPaddings.all(8),
            fontWeight: FontWeight.bold,
            textAlign: TextAlign.center,
            border: Border.all(color: Colors.grey[300]!),
          ),
          "td": Style(
            padding: HtmlPaddings.all(8),
            border: Border.all(color: Colors.grey[300]!),
            textAlign: TextAlign.center,
          ),
          "tr:nth-child(even)": Style(backgroundColor: Colors.grey[50]),
          "a": Style(
            color: Colors.blue,
            textDecoration: TextDecoration.underline,
          ),
        },
        onLinkTap: (url, attributes, element) {
          // Handle link taps if needed
          // print('Link tapped: $url'); // Commented out for production
        },
      ),
    );
  }

  String _getHistoryData() {
    // Since we don't have the actual histori_bid field in our model,
    // we'll create a sample history based on available data
    if (widget.ikanLelang.jmlBid.isEmpty || widget.ikanLelang.jmlBid == '0') {
      return '';
    }

    // Create a sample bid history table
    return '''
    <table style="width: 100%; border-collapse: collapse;">
      <thead>
        <tr>
          <th>No</th>
          <th>Bidder</th>
          <th>Nilai Bid</th>
          <th>Waktu</th>
        </tr>
      </thead>
      <tbody>
        ${_generateSampleBidHistory()}
      </tbody>
    </table>
    ''';
  }

  String _generateSampleBidHistory() {
    final bidCount = int.tryParse(widget.ikanLelang.jmlBid) ?? 0;
    if (bidCount == 0) return '';

    final StringBuffer history = StringBuffer();

    // Generate sample bid history entries
    for (int i = bidCount; i >= 1; i--) {
      final bidNumber = bidCount - i + 1;
      final bidderName = 'Bidder${i.toString().padLeft(3, '0')}';
      final bidValue = _calculateBidValue(i);
      final bidTime = _generateBidTime(i);

      history.write('''
        <tr>
          <td>$bidNumber</td>
          <td>$bidderName</td>
          <td style="font-weight: bold; color: ${_getBidColor(i)};">$bidValue</td>
          <td>$bidTime</td>
        </tr>
      ''');
    }

    return history.toString();
  }

  String _calculateBidValue(int bidIndex) {
    // Calculate bid value based on OB and KB
    final ob = _parsePrice(widget.ikanLelang.ob);
    final kb = _parsePrice(widget.ikanLelang.kb);

    final bidValue = ob + (kb * (bidIndex - 1));
    return _formatPrice(bidValue);
  }

  int _parsePrice(String priceString) {
    // Remove non-numeric characters and parse
    final cleanPrice = priceString.replaceAll(RegExp(r'[^\d]'), '');
    return int.tryParse(cleanPrice) ?? 0;
  }

  String _formatPrice(int price) {
    // Format price with Indonesian currency format
    final formatter = price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );
    return 'Rp $formatter';
  }

  String _getBidColor(int bidIndex) {
    // Alternate colors for bids
    return bidIndex % 2 == 0 ? '#2196F3' : '#4CAF50';
  }

  String _generateBidTime(int bidIndex) {
    // Generate sample bid times (working backwards from current time)
    final now = DateTime.now();
    final bidTime = now.subtract(Duration(minutes: bidIndex * 5));

    return '${bidTime.day.toString().padLeft(2, '0')}/'
        '${bidTime.month.toString().padLeft(2, '0')}/'
        '${bidTime.year} '
        '${bidTime.hour.toString().padLeft(2, '0')}:'
        '${bidTime.minute.toString().padLeft(2, '0')}';
  }
}
