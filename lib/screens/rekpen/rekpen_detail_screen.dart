import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/rekpen.dart';
import '../../providers/rekpen_provider.dart';
import '../../utils/colors.dart';
import '../../widgets/whatsapp_button.dart';
import 'rekpen_edit_rekening_screen.dart';

class RekpenDetailScreen extends StatefulWidget {
  final String idRekpen;

  const RekpenDetailScreen({
    Key? key,
    required this.idRekpen,
  }) : super(key: key);

  @override
  State<RekpenDetailScreen> createState() => _RekpenDetailScreenState();
}

class _RekpenDetailScreenState extends State<RekpenDetailScreen> {
  @override
  void initState() {
    super.initState();
    
    // Load data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<RekpenProvider>(context, listen: false);
      provider.fetchRekpenDetail(widget.idRekpen);
    });
  }
  
  // Fungsi untuk menampilkan dialog konfirmasi PIN
  Future<void> _showPinDialog({
    required String title,
    required String message,
    required Function(String) onConfirm,
  }) async {
    final pinController = TextEditingController();
    
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message),
            const SizedBox(height: 16),
            TextField(
              controller: pinController,
              decoration: const InputDecoration(
                labelText: 'PIN',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              maxLength: 4,
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm(pinController.text);
            },
            child: const Text('Konfirmasi'),
          ),
        ],
      ),
    );
  }
  
  // Fungsi untuk melakukan aksi pada rekpen
  Future<void> _performAction(String action, String actionText) async {
    final provider = Provider.of<RekpenProvider>(context, listen: false);
    
    await _showPinDialog(
      title: 'Konfirmasi $actionText',
      message: 'Apakah Anda yakin ingin $actionText rekpen ini?',
      onConfirm: (pin) async {
        final success = await provider.rekpenAction(
          action: action,
          idRekpen: widget.idRekpen,
          pin: pin,
        );
        
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Berhasil $actionText rekpen'),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Detail Rekpen'),
      ),
      body: Consumer<RekpenProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (provider.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    provider.errorMessage,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.fetchRekpenDetail(widget.idRekpen);
                    },
                    child: const Text('Coba Lagi'),
                  ),
                ],
              ),
            );
          }
          
          final Rekpen rekpen = provider.selectedRekpen!;

          // Parse warna dari string hex
          Color statusBgColor = Color(int.parse('0xFF${rekpen.colorBgStatusPayment.replaceAll('#', '')}'));
          Color statusTextColor = Color(int.parse('0xFF${rekpen.colorTextStatusPayment.replaceAll('#', '')}'));
          
          return RefreshIndicator(
            onRefresh: () => provider.fetchRekpenDetail(widget.idRekpen),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Token Rekpen
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'Token: ',
                                style: TextStyle(
                                  color: Colors.grey,
                                ),
                              ),
                              Text(
                                rekpen.tokenRekpen,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.copy, size: 18),
                                onPressed: () {
                                  Clipboard.setData(ClipboardData(text: rekpen.tokenRekpen));
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Token Rekpen berhasil disalin'),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: statusBgColor,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              rekpen.povStatus,
                              style: TextStyle(
                                color: statusTextColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Informasi Pengirim dan Penerima
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Kirim Ke',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                              Text(
                                ' ▶︎▶︎▶︎ ',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                              Text(
                                rekpen.noHpPenerima,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: WhatsAppButton(
                                  buttonText: 'Chat Pengirim',
                                  icon: Icons.chat,
                                  replacements: {
                                    'message': 'Hai Pengirim Rekpen, saya ingin menindaklanjuti rekpen berikut :\n${rekpen.tokenRekpen} atau klik di http://mrkoi.bid/rkp/${rekpen.idRekpen}',
                                  },
                                  backgroundColor: AppColors.primary,
                                  showAdminSelection: false,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: WhatsAppButton(
                                  buttonText: 'Chat Penerima',
                                  icon: Icons.chat,
                                  replacements: {
                                    'message': 'Hai Penerima Rekpen, saya ingin menindaklanjuti rekpen berikut :\n${rekpen.tokenRekpen} atau klik di http://mrkoi.bid/rkp/${rekpen.idRekpen}',
                                  },
                                  backgroundColor: AppColors.primaryLight,
                                  showAdminSelection: false,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Informasi Nilai
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildInfoRow('Nilai Rekpen', rekpen.viewNilaiRekpen),
                          const Divider(),
                          _buildInfoRow('Biaya Rekpen', rekpen.viewBiayaRekpen),
                          const Divider(),
                          _buildInfoRow('Total Rekpen', rekpen.viewTotalRekpen, isBold: true),
                          if (rekpen.keterangan.isNotEmpty) ...[
                            const Divider(),
                            _buildInfoRow('Keterangan', rekpen.keterangan),
                          ],
                          if (rekpen.nomorRekeningForward.isNotEmpty) ...[
                            const Divider(),
                            _buildInfoRow(
                              'Rekening',
                              '${rekpen.bankForward} - ${rekpen.nomorRekeningForward} a.n ${rekpen.atasNamaForward}',
                            ),
                            if (rekpen.showEditRekening == 1)
                              TextButton.icon(
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => RekpenEditRekeningScreen(
                                        idRekpen: rekpen.idRekpen,
                                        currentBank: rekpen.bankForward,
                                        currentRekening: rekpen.nomorRekeningForward,
                                        currentAtasNama: rekpen.atasNamaForward,
                                      ),
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.edit),
                                label: const Text('Edit Rekening'),
                              ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Tombol Aksi
                  if (rekpen.showBayarSekarangPengirim == 1)
                    _buildActionButton(
                      'Bayar Sekarang',
                      Icons.payment,
                      Colors.green,
                      () async {
                        if (rekpen.linkPaymentRekpen.isNotEmpty) {
                          final url = Uri.parse(rekpen.linkPaymentRekpen);
                          if (await canLaunchUrl(url)) {
                            await launchUrl(url, mode: LaunchMode.externalApplication);
                          }
                        }
                      },
                    ),
                  
                  if (rekpen.showTeruskanDanaPengirim == 1)
                    _buildActionButton(
                      'Teruskan Dana',
                      Icons.send,
                      Colors.blue,
                      () => _performAction('teruskan-dana', 'teruskan dana'),
                    ),
                  
                  if (rekpen.showBatalkanPengirim == 1)
                    _buildActionButton(
                      'Batalkan',
                      Icons.cancel,
                      Colors.red,
                      () => _performAction('batal', 'batalkan'),
                    ),
                  
                  if (rekpen.showSetujuiPengembalianDana == 1)
                    _buildActionButton(
                      'Setujui Pengembalian Dana',
                      Icons.thumb_up,
                      Colors.orange,
                      () => _performAction('setujui-pengembalian-dana', 'setujui pengembalian dana'),
                    ),
                  
                  if (rekpen.showAjukanRefund == 1)
                    _buildActionButton(
                      'Ajukan Refund',
                      Icons.money_off,
                      Colors.deepOrange,
                      () => _performAction('ajukan-refund', 'ajukan refund'),
                    ),
                  
                  if (rekpen.showAjukanPencairanDanaPenerima == 1)
                    _buildActionButton(
                      'Ajukan Pencairan Dana',
                      Icons.monetization_on,
                      Colors.green,
                      () => _performAction('ajukan-pencairan-dana', 'ajukan pencairan dana'),
                    ),
                  
                  if (rekpen.showRekeningSudahSesuai == 1)
                    _buildActionButton(
                      'Rekening Sudah Sesuai',
                      Icons.check_circle,
                      Colors.green,
                      () => _performAction('rekening-sudah-sesuai', 'konfirmasi rekening sudah sesuai'),
                    ),
                  
                  if (rekpen.showAktifkanRekpenPengirim == 1)
                    _buildActionButton(
                      'Aktifkan Rekpen',
                      Icons.power_settings_new,
                      Colors.blue,
                      () => _performAction('aktifkan-rekpen', 'aktifkan rekpen'),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
              style: TextStyle(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: isBold ? AppColors.primary : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon),
          label: Text(text),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
        ),
      ),
    );
  }
}
