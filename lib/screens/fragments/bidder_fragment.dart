import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:masterkoi_app/widgets/reklame_widget.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../config/flavor_config.dart';
import '../../models/point_level.dart';
import '../../models/user_points.dart';
import '../../services/api_service.dart';
import '../../utils/card_view_menu.dart';
import '../../utils/constants.dart';
import '../../utils/header_banner.dart';
import '../../utils/preference_manager.dart';
import '../../utils/warning_message.dart';



class CardViewMenuWebview extends StatelessWidget {
  final IconData icon;
  final String text;
  final String group;
  final Function(String url) onTap;
  const CardViewMenuWebview({
    super.key,
    required this.icon,
    required this.text,
    required this.group,
    required this.onTap,
  });
  @override
  Widget build(BuildContext context) {
    // For demo, just use a dummy url
    String url = "https://masterkoi.bid/$text".toLowerCase().replaceAll(' ', '-');
    return Card(
      child: InkWell(
        onTap: () => onTap(url),
        child: SizedBox(
          height: 80,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(height: 4),
              Text(text, style: const TextStyle(fontSize: 12)),
            ],
          ),
        ),
      ),
    );
  }
}

class WebViewScreen extends StatelessWidget {
  final String url;
  final String? title;
  final Function(String fishId)? onFishDetailNavigate;

  const WebViewScreen({
    super.key,
    required this.url,
    this.title,
    this.onFishDetailNavigate
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title ?? 'WebView'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text("Loading: $url"),
              const SizedBox(height: 16),
              const Text("WebView implementation needed"),
            ],
          ),
        ),
      ),
    );
  }
}

class PointHistoryScreen extends StatelessWidget {
  const PointHistoryScreen({super.key});
  @override
  Widget build(BuildContext context) {
    // Placeholder for point history
    return Scaffold(
      appBar: AppBar(title: const Text("Riwayat Poin")),
      body: const Center(child: Text("Daftar Riwayat Poin")),
    );
  }
}

class BidderFragment extends StatefulWidget {
  final Function(String)? onWebNavigate;
  final Function()? onRekpenNavigate;
  final Function()? showDialogLogin;
  final Function()? showDialogBukanBidderInfo;
  final Function()? showPengembangan;

  const BidderFragment({
    Key? key,
    this.onWebNavigate,
    this.onRekpenNavigate,
    this.showDialogLogin,
    this.showDialogBukanBidderInfo,
    this.showPengembangan,
  }) : super(key: key);

  @override
  State<BidderFragment> createState() => _BidderFragmentState();
}

class _BidderFragmentState extends State<BidderFragment> {
  bool isBuyer = false;
  bool isLoading = false;
  bool isLoadingPoints = false;
  bool showWebView = false;
  String webUrl = '';
  int selectedTab = 0;

  // Points data
  String viewSummaryPointReward = '';
  double summaryPointReward = 0;
  String viewBalancePointReward = '';
  double balancePointReward = 0;

  int idPointLevel = 0;
  int minPointLevel = 0;
  int maxPointLevel = 0;
  String namePointLevel = '';
  String iconPointLevel = '';

  String coupon = '';
  String couponWarning = '';

  String fishId = '';
  bool navigateToFishDetail = false;

  final prefs = PreferenceManager();


  @override
  void initState() {
    super.initState();
    _checkBuyerStatus();
  }

  void _checkBuyerStatus() {
    final buyerList = prefs.loadPreference('list_buyer');
    setState(() {
      isBuyer = buyerList.isNotEmpty;
      if (isBuyer) {
        _getPoints();
      }
    });
  }

  Future<void> _getPoints() async {
    setState(() {
      isLoadingPoints = true;
    });

    try {
      final response = await ApiService().getPoints();

      if (response['statusCode'] == 200) {
        final data = response['data'];
        final UserPoints userPoints = UserPoints.fromJson(data);

        setState(() {
          summaryPointReward = userPoints.summaryPointReward;
          viewSummaryPointReward = userPoints.viewSummaryPointReward;
          balancePointReward = userPoints.balancePointReward;
          viewBalancePointReward = userPoints.viewBalancePointReward;

          idPointLevel = userPoints.pointLevel.id;
          minPointLevel = userPoints.pointLevel.minPoints;
          maxPointLevel = userPoints.pointLevel.maxPoints;
          namePointLevel = userPoints.pointLevel.name;
          iconPointLevel = userPoints.pointLevel.iconUrl;

          coupon = userPoints.coupon;
          couponWarning = userPoints.couponWarning;
        });
      } else {
        setState(() {
          viewBalancePointReward = response['message'] ?? 'Error loading points';
          coupon = '';
          couponWarning = '';
        });
        _showDialog('Error', response['message'] ?? 'Failed to load points');
      }
    } catch (e) {
      setState(() {
        viewBalancePointReward = 'Network error';
        coupon = '';
        couponWarning = '';
      });
      _showDialog('Error', 'Network error: $e');
    } finally {
      setState(() {
        isLoadingPoints = false;
      });
    }
  }

  Future<void> _launchWhatsApp(String phone, String message) async {
    final encodedMessage = Uri.encodeComponent(message);
    final url = 'https://api.whatsapp.com/send/?phone=$phone&text=$encodedMessage&type=phone_number&app_absent=0';
    final uri = Uri.parse(url);

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showDialog('Informasi', 'WhatsApp tidak terinstall di perangkat ini.');
      }
    } catch (e) {
      _showDialog('Error', 'Gagal membuka WhatsApp: $e');
    }
  }

  void _showDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            const WarningMessageView(),
            Expanded(
              child: Stack(
                children: [
                  if (!showWebView)
                    _buildMainContent(),
                  if (showWebView)
                    _buildWebView(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Reklame widget placeholder - implement if needed
          // const ReklameWidget(reklameList: reklameList),

          // User profile section for logged in bidders
          if (isBuyer) _buildUserProfileHeader(),

          HeaderBanner(
            imageNumber: 'gambar3.png',
            text: 'Terintegrasi Real-Time antara WhatsApp dan Telegram, serta terpublis di Official Web 😎',
            height: 160,
          ),
          if (prefs.loadPreference('as_bnr') == '1')
            _buildBnrWarningButton(),
          _buildMenuGrid(),
        ],
      ),
    );
  }



  Widget _buildUserProfileHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    prefs.loadPreference('nama_buyer'),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    prefs.loadPreference('no_hp_buyer'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PointHistoryScreen(),
                  ),
                );
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isLoadingPoints)
                      const CircularProgressIndicator(color: Colors.white)
                    else ...[
                      SizedBox(
                        width: 60,
                        height: 60,
                        child: iconPointLevel.isNotEmpty
                            ? Image.network(
                                iconPointLevel,
                                errorBuilder: (_, __, ___) => const Icon(
                                  Icons.military_tech,
                                  size: 40,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(
                                Icons.military_tech,
                                size: 40,
                                color: Colors.white,
                              ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.remove_red_eye,
                            color: Colors.white,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            viewBalancePointReward,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBnrWarningButton() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ElevatedButton(
        onPressed: () {
          _launchWhatsApp(
              prefs.loadPreference('nomor_bantuan'),
            'Saya dilaporkan sebagai pelaku BNR, Mohon bantuan mediasinya.',
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [
              Icon(Icons.message, color: Colors.white),
              SizedBox(width: 8),
              Text(
                'Anda dilaporkan BNR, klik info selengkapnya',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuGrid() {
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Column(
        children: [
          _buildMonthlyCouponCard(),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.poll,
                  text: 'Dashboard Bidder',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/dashboard');
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.person_outline,
                  text: 'Profil Bidder',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/profile');
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuWebview(
                  icon: Icons.military_tech,
                  text: 'Poin',
                  group: 'Bidder',
                  onTap: (url) {
                    setState(() {
                      showWebView = true;
                      webUrl = url;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.gavel,
                  text: 'BID Saya',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/bids');
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.pets,
                  text: 'Ikan Saya',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/fish');
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.favorite,
                  text: 'Wishlist',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/wishlist');
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CardViewMenuItem(
                  icon: Icons.history,
                  text: 'Histori BID',
                  onTap: () {
                    Navigator.pushNamed(context, '/bidder/history');
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuWebview(
                  icon: Icons.credit_card,
                  text: 'Rekening Penjamin',
                  group: 'Seller',
                  onTap: (url) {
                    if (widget.onRekpenNavigate != null) {
                      widget.onRekpenNavigate!();
                    } else {
                      setState(() {
                        showWebView = true;
                        webUrl = url;
                      });
                    }
                  },
                ),
              ),
              Expanded(
                child: CardViewMenuWebview(
                  icon: Icons.link_off,
                  text: 'BNR',
                  group: 'Seller',
                  onTap: (url) {
                    setState(() {
                      showWebView = true;
                      webUrl = url;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CardViewMenuWebview(
                  icon: Icons.credit_card,
                  text: 'Ikan BNR',
                  group: 'Bidder',
                  onTap: (url) {
                    setState(() {
                      showWebView = true;
                      webUrl = url;
                    });
                  },
                ),
              ),
              Expanded(child: Container()),
              Expanded(child: Container()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyCouponCard() {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, '/bidder/monthly-draw');
          },
          child: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.card_giftcard,
                        color: Theme.of(context).primaryColor,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Undian Bulanan',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                if (coupon.isNotEmpty)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            coupon,
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          Text(
                            couponWarning,
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          child: Image.asset(
            'assets/images/logo_new.png',
            height: 60,
          ),
        ),
      ],
    );
  }

  Widget _buildWebView() {
    return Stack(
      children: [
        WebViewScreen(
          url: webUrl,
          onFishDetailNavigate: (fishId) {
            setState(() {
              this.fishId = fishId;
              navigateToFishDetail = true;
              showWebView = false;
            });
          },
        ),
        if (isLoading)
          const Center(
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }
}
