import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../config/flavor_config.dart';
import '../../providers/rekpen_provider.dart';
import '../../widgets/rekpen_item.dart';
import '../../widgets/rekpen_status_item.dart';
import '../rekpen/rekpen_detail_screen.dart';
import '../rekpen/rekpen_create_screen.dart';
import '../../utils/colors.dart';

class RekpenScreen extends StatefulWidget {
  const RekpenScreen({Key? key}) : super(key: key);

  @override
  State<RekpenScreen> createState() => _RekpenScreenState();
}

class _RekpenScreenState extends State<RekpenScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Load data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<RekpenProvider>(context, listen: false);
      provider.fetchRekpenStatus();
      provider.fetchRekpenList(refresh: true);
    });

    // Setup scroll controller for pagination
    _scrollController.addListener(_onScroll);

    // Setup search controller listener for real-time search
    _searchController.addListener(() {
      // Update the UI to show/hide clear button
      setState(() {});
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      final provider = Provider.of<RekpenProvider>(context, listen: false);
      if (!provider.isLoading && provider.hasMoreData) {
        provider.fetchRekpenList();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<RekpenProvider>(
        builder: (context, provider, child) {
          return RefreshIndicator(
            onRefresh: () => provider.fetchRekpenList(refresh: true),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Header with image background
                SliverToBoxAdapter(
                  child: _buildHeaderView(),
                ),

                // Button Input Kirim Dana
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const RekpenCreateScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.add_circle),
                      label: const Text('Input Kirim Dana'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                  ),
                ),

                // Search Bar
                SliverToBoxAdapter(
                  child: _buildSearchBar(provider),
                ),

                // Status Filter
                SliverToBoxAdapter(
                  child: _buildStatusButtons(provider),
                ),

                // Rekpen List
                _buildRekpenList(provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderView() {
    return Stack(
      children: [
        Image.asset(
          "assets/images/${FlavorConfig.instance.flavor.name}/gambar2.png",
          height: 160,
          width: double.infinity,
          fit: BoxFit.cover,
        ),
        Container(
          height: 160,
          color: Colors.black.withOpacity(0.6),
        ),
        SizedBox(
          height: 160,
          child: Row(
            children: [
              Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Rekening Penjamin',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  _buildHeaderButton(
                    'Tutorial Rekpen',
                    Icons.play_circle_fill,
                    Colors.red,
                    () => _launchYouTube(),
                  ),
                  _buildHeaderButton(
                    'Bantuan CS',
                    Icons.chat,
                    Colors.white,
                    () => _launchWhatsApp(),
                    textColor: AppColors.primary,
                    borderColor: AppColors.primary,
                  ),
                ],
              ),
              Spacer()
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderButton(
    String text,
    IconData icon,
    Color backgroundColor,
    VoidCallback onPressed, {
    Color textColor = Colors.white,
    Color? borderColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: borderColor != null
              ? BorderSide(color: borderColor, width: 2)
              : BorderSide.none,
        ),
      ),
    );
  }

  Widget _buildSearchBar(RekpenProvider provider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.search),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search',
                  border: InputBorder.none,
                ),
                onChanged: (value) {
                  // Immediate search like MasterKoiBot
                  provider.setSearchQuery(value);
                },
              ),
            ),
            if (_searchController.text.isNotEmpty)
              IconButton(
                onPressed: () {
                  _searchController.clear();
                  provider.setSearchQuery('');
                },
                icon: const Icon(Icons.clear),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButtons(RekpenProvider provider) {
    return SizedBox(
      height: 60,
      child: provider.rekpenStatusList.isEmpty
          ? const Center(child: Text('Tidak ada status rekpen'))
          : ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              itemCount: provider.rekpenStatusList.length,
              itemBuilder: (context, index) {
                final status = provider.rekpenStatusList[index];
                final isSelected = provider.selectedStatus?.idStatusPayment == status.idStatusPayment;

                return RekpenStatusItem(
                  status: status,
                  isSelected: isSelected,
                  onTap: () {
                    provider.setSelectedStatus(status);
                  },
                );
              },
            ),
    );
  }

  Widget _buildRekpenList(RekpenProvider provider) {
    if (provider.errorMessage.isNotEmpty && provider.errorMessage != "Berhasil") {
      return SliverFillRemaining(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              provider.errorMessage,
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    if (provider.isLoading && provider.rekpenList.isEmpty) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (provider.rekpenList.isEmpty) {
      return const SliverFillRemaining(
        child: Center(child: Text('Tidak ada data rekpen')),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(4),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index == provider.rekpenList.length) {
              return provider.isLoading
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : const SizedBox();
            }

            final rekpen = provider.rekpenList[index];

            return Padding(
              padding: const EdgeInsets.all(2),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: RekpenItem(
                  rekpen: rekpen,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => RekpenDetailScreen(
                          idRekpen: rekpen.idRekpen,
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          },
          childCount: provider.rekpenList.length + (provider.hasMoreData ? 1 : 0),
        ),
      ),
    );
  }

  void _launchYouTube() async {
    const youtubeUrl = 'https://www.youtube.com/watch?v=KWGpGJMtZG8';
    const youtubeAppUrl = 'youtube://watch?v=KWGpGJMtZG8';

    try {
      if (await canLaunchUrl(Uri.parse(youtubeAppUrl))) {
        await launchUrl(Uri.parse(youtubeAppUrl));
      } else {
        await launchUrl(Uri.parse(youtubeUrl));
      }
    } catch (e) {
      // Handle error
      debugPrint('Could not launch YouTube: $e');
    }
  }

  void _launchWhatsApp() async {
    // Implement WhatsApp CS contact
    const whatsappUrl = 'https://wa.me/6281234567890'; // Replace with actual CS number

    try {
      await launchUrl(Uri.parse(whatsappUrl));
    } catch (e) {
      // Handle error
      debugPrint('Could not launch WhatsApp: $e');
    }
  }
}
