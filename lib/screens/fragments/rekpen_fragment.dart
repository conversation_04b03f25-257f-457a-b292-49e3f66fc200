import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/flavor_config.dart';
import '../../providers/rekpen_provider.dart';
import '../../utils/header_banner.dart';
import '../../widgets/rekpen_item.dart';
import '../../widgets/rekpen_status_item.dart';
import '../rekpen/rekpen_detail_screen.dart';
import '../rekpen/rekpen_create_screen.dart';
import '../../utils/colors.dart';

class RekpenScreen extends StatefulWidget {
  const RekpenScreen({Key? key}) : super(key: key);

  @override
  State<RekpenScreen> createState() => _RekpenScreenState();
}

class _RekpenScreenState extends State<RekpenScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    
    // Load data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<RekpenProvider>(context, listen: false);
      provider.fetchRekpenStatus();
      provider.fetchRekpenList(refresh: true);
    });
    
    // Setup scroll controller for pagination
    _scrollController.addListener(_onScroll);
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      final provider = Provider.of<RekpenProvider>(context, listen: false);
      if (!provider.isLoading && provider.hasMoreData) {
        provider.fetchRekpenList();
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<RekpenProvider>(
        builder: (context, provider, child) {
          return RefreshIndicator(
            onRefresh: () => provider.fetchRekpenList(refresh: true),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  expandedHeight: 160,
                  pinned: true,
                  flexibleSpace: FlexibleSpaceBar(
                    title: const Text('Rekening Penjamin'),
                    background: HeaderBanner(
                      imageNumber: 'gambar1.png',
                      text: 'Platform Robot Lelang Indonesia Online 🇮🇩',
                    ),
                  ),
                ),

                // Button Input Kirim Dana
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const RekpenCreateScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.add_circle),
                      label: const Text('Input Kirim Dana'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accent,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                  ),
                ),
                
                // Search Bar
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Cari Rekpen',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                      onSubmitted: (value) {
                        provider.setSearchQuery(value);
                      },
                    ),
                  ),
                ),
                
                // Status Filter
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 50,
                    child: provider.rekpenStatusList.isEmpty
                        ? const Center(child: Text('Tidak ada status rekpen'))
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            itemCount: provider.rekpenStatusList.length,
                            itemBuilder: (context, index) {
                              final status = provider.rekpenStatusList[index];
                              final isSelected = provider.selectedStatus?.idStatusPayment == status.idStatusPayment;
                              
                              return RekpenStatusItem(
                                status: status,
                                isSelected: isSelected,
                                onTap: () {
                                  provider.setSelectedStatus(status);
                                },
                              );
                            },
                          ),
                  ),
                ),
                
                // Rekpen List
                provider.isLoading && provider.rekpenList.isEmpty
                    ? const SliverFillRemaining(
                        child: Center(child: CircularProgressIndicator()),
                      )
                    : provider.rekpenList.isEmpty
                        ? const SliverFillRemaining(
                            child: Center(child: Text('Tidak ada data rekpen')),
                          )
                        : SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                if (index == provider.rekpenList.length) {
                                  return provider.isLoading
                                      ? const Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(16),
                                            child: CircularProgressIndicator(),
                                          ),
                                        )
                                      : const SizedBox();
                                }
                                
                                final rekpen = provider.rekpenList[index];
                                
                                return RekpenItem(
                                  rekpen: rekpen,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => RekpenDetailScreen(
                                          idRekpen: rekpen.idRekpen,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              childCount: provider.rekpenList.length + (provider.hasMoreData ? 1 : 0),
                            ),
                          ),
              ],
            ),
          );
        },
      ),
    );
  }
}
