import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../utils/card_view_menu.dart';
import '../utils/header_banner.dart';

class HomeScreen extends StatefulWidget {
  final bool webPage;
  final ValueChanged<int> onSelectionChanged;
  final int selection;

  const HomeScreen({
    Key? key,
    this.webPage = false,
    required this.onSelectionChanged,
    required this.selection,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late WebViewController _webViewController;
  bool isLoadingQRCode = false;
  bool isLoadingDaftar = false;
  bool isLoading = true;
  String url = "";
  bool isWebViewRefreshed = false;
  bool isNetworkAvailable = true;
  String idObyekLelang = "";
  bool keDetailIkan = false;
  bool isPresentingQRCode = false;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (String url) {
          setState(() {
            isLoading = true;
          });
        },
        onPageFinished: (String url) {
          setState(() {
            isLoading = false;
          });
        },
      ));
  }

  void _handleWebView(String route) {
    final baseUrl = FlavorConfig.instance.baseUrl;
    final fullUrl = '$baseUrl$route';

    setState(() {
      url = fullUrl;
      _webViewController.loadRequest(Uri.parse(fullUrl));
      widget.onSelectionChanged(0); // This will trigger the parent to show web page
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (widget.webPage) {
          widget.onSelectionChanged(2); // Return to default tab
          return false;
        }
        return true;
      },
      child: Scaffold(
        body: Stack(
          children: [
            if (!widget.webPage)
              SingleChildScrollView(
                child: Column(
                  children: [
                    // Hero Banner Section
                    HeaderBanner(
                      imageNumber: 'gambar1.png',
                      text: 'Terintegrasi Real-Time antara WhatsApp dan Telegram, serta terpublis di Official Web 😎',
                      height: 160,
                    ),


                    Padding(
                      padding: const EdgeInsets.all(2.0),
                      child: Column(
                        children: [
                          // Bonus Affiliate Card
                          _buildAffiliateCard(),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.tv,
                                  text: 'Live Lelang',
                                  onTap: () => _handleWebView('/live/lelang'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.fish,
                                  text: 'Opsi Deal',
                                  onTap: () {},
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.code,
                                  text: 'KC / Azhukari',
                                  onTap: () => _handleWebView('/lelang/kc'),
                                ),
                              ),
                            ],
                          ),

                          // Second Row
                          Row(
                            children: [
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.award,
                                  text: 'Top Bidder',
                                  onTap: () => _handleWebView('/bidder/top'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.trophy,
                                  text: 'Top Seller',
                                  onTap: () => _handleWebView('/seller/top'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.medal,
                                  text: 'Most Auction',
                                  onTap: () => _handleWebView('/seller/most-auction'),
                                ),
                              ),
                            ],
                          ),

                          // Third Row
                          Row(
                            children: [
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.store,
                                  text: 'Info Seller',
                                  onTap: () => _handleWebView('/seller'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.edit,
                                  text: 'Pendaftaran Seller',
                                  onTap: () => _openWhatsapp(),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.dollarSign,
                                  text: 'Tarif Fee BOT',
                                  onTap: () => _handleWebView('/#tarif'),
                                ),
                              ),
                            ],
                          ),

                          // Fourth Row
                          Row(
                            children: [
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.unlink,
                                  text: 'Pelaku BNR',
                                  onTap: () => _handleWebView('/bnr'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.storeSlash,
                                  text: 'Seller Bad Debt',
                                  onTap: () => _handleWebView('/seller/baddebt'),
                                ),
                              ),
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.ruler,
                                  text: 'Rule',
                                  onTap: () => _handleWebView('/rule'),
                                ),
                              ),
                            ],
                          ),

                          // Fifth Row
                          Row(
                            children: [
                              Expanded(
                                child: CardViewMenuItem(
                                  icon: FontAwesomeIcons.flag,
                                  text: 'Tutorial',
                                  onTap: () => _handleWebView('/tutorial'),
                                ),
                              ),
                              Expanded(child: SizedBox()), // Empty space
                              Expanded(child: SizedBox()), // Empty space
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            if (widget.webPage)
              Stack(
                children: [
                  WebViewWidget(
                    controller: _webViewController,
                  ),
                  if (isLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAffiliateCard() {
    return Stack(
      children: [
        Card(
          child: InkWell(
            onTap: () {
              // Handle QR Code presentation
              if (_hasValidSession()) {
                setState(() {
                  isPresentingQRCode = true;
                });
              } else {
                // Show phone verification
                _showVerificationPhone();
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  if (isLoadingQRCode)
                    const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(),
                    )
                  else
                    const Icon(
                      FontAwesomeIcons.gift,
                      size: 24,
                      color: Colors.blue,
                    ),
                  const SizedBox(width: 8),
                  Text(
                    'Bonus Affiliate',
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          left: 0,
          top: 0,
          child: Image.asset(
            'assets/images/logo_new.png',
            height: 60,
          ),
        ),
      ],
    );
  }

  bool _hasValidSession() {
    // Implement your session validation logic here
    return false;
  }

  void _showVerificationPhone() {
    // Implement your phone verification logic here
  }

  Future<void> _openWhatsapp() async {
    setState(() {
      isLoadingDaftar = true;
    });

    try {
      // Use FlavorConfig for proper API URL
      final baseUrl = FlavorConfig.instance.baseUrl;
      final response = await http.get(
        Uri.parse('$baseUrl/api/seller/cs'),
        headers: {
          'Session': 'YOUR_SESSION_TOKEN', // TODO: Get from preferences
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['meta']['code'] == 200) {
          final phoneNumber = data['data'];
          final url =
              'https://api.whatsapp.com/send/?phone=$phoneNumber&text=DAFTAR&type=phone_number&app_absent=0';
          final uri = Uri.parse(url);

          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          } else {
            _showDialog('WhatsApp tidak terinstall di perangkat ini.');
          }
        } else {
          _showDialog(data['meta']['message'] ?? 'Gagal mendapatkan nomor CS.');
        }
      } else {
        _showDialog('Gagal menghubungi server.');
      }
    } catch (e) {
      _showDialog('Terjadi kesalahan saat menghubungi server: $e');
    } finally {
      setState(() {
        isLoadingDaftar = false;
      });
    }
  }

  void _showDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Informasi'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
