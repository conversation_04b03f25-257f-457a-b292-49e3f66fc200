import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../../models/ikan_lelang.dart';
import '../../providers/auth_provider.dart';
import '../../services/ikan_lelang_service.dart';
import '../../utils/colors.dart';
import '../../utils/constants.dart';
import '../../widgets/filter_dialog.dart';
import '../../widgets/sort_dialog.dart';
import '../ikan_lelang/ikan_detail_screen.dart';

class IkanLelangFragment extends StatefulWidget {
  const IkanLelangFragment({Key? key}) : super(key: key);

  @override
  State<IkanLelangFragment> createState() => _IkanLelangFragmentState();
}

class _IkanLelangFragmentState extends State<IkanLelangFragment> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  String _searchQuery = "";
  String _sortBy = "";
  Map<String, dynamic> _filters = {};

  List<IkanLelang> _ikanList = [];
  List<dynamic> _sizeIkanList = [];
  List<dynamic> _levelSellerList = [];
  List<dynamic> _modeLelangList = [];
  List<dynamic> _lokasiIkanList = [];

  // Timer related variables
  Map<String, Timer> _timers = {};
  Map<String, Duration> _remainingTimes = {};

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    // Cancel all active timers
    _timers.forEach((_, timer) => timer.cancel());
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
      });
    }

    try {
      // Load filter options
      await _loadFilterOptions();

      // Load ikan lelang data
      final ikanLelangService = IkanLelangService();
      final result = await ikanLelangService.getIkanLelangList(
        page: _currentPage,
        search: _searchQuery,
        sort: _sortBy,
        filters: _filters,
      );

      if (mounted) {
        setState(() {
          _ikanList = result;
          _isLoading = false;
          _hasMoreData = result.length >= 10; // Assuming 10 items per page
        });

        // Start countdown timers for each ikan
        for (var ikan in _ikanList) {
          _startCountdown(ikan);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorDialog("Gagal memuat data: ${e.toString()}");
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (mounted) {
      setState(() {
        _isLoadingMore = true;
        _currentPage++;
      });
    }

    try {
      final ikanLelangService = IkanLelangService();
      final result = await ikanLelangService.getIkanLelangList(
        page: _currentPage,
        search: _searchQuery,
        sort: _sortBy,
        filters: _filters,
      );

      if (mounted) {
        setState(() {
          _ikanList.addAll(result);
          _isLoadingMore = false;
          _hasMoreData = result.length >= 10; // Assuming 10 items per page
        });

        // Start countdown timers for each ikan
        for (var ikan in result) {
          _startCountdown(ikan);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          _currentPage--; // Revert page increment on error
        });
      }
    }
  }

  Future<void> _loadFilterOptions() async {
    try {
      final ikanLelangService = IkanLelangService();
      final result = await ikanLelangService.getFilterOptions();

      if (mounted) {
        setState(() {
          _sizeIkanList = result['size_ikan'] ?? [];
          _levelSellerList = result['level_seller'] ?? [];
          _modeLelangList = result['mode_lelang'] ?? [];
          _lokasiIkanList = result['lokasi_ikan'] ?? [];
        });
      }
    } catch (e) {
      // Handle error silently
      debugPrint('Error loading filter options: ${e.toString()}');
    }
  }

  void _onSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadData();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(
        sizeIkanList: _sizeIkanList,
        levelSellerList: _levelSellerList,
        modeLelangList: _modeLelangList,
        lokasiIkanList: _lokasiIkanList,
        currentFilters: _filters,
        onApplyFilter: (filters) {
          setState(() {
            _filters = filters;
          });
          _loadData();
        },
        onReset: _resetFilters,
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => SortDialog(
        currentSort: _sortBy,
        onSortSelected: (sortBy) {
          setState(() {
            _sortBy = sortBy;
          });
          _loadData();
        },
        onReset: _resetFilters,
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _sortBy = "";
      _filters.clear();
      _searchQuery = "";
      _searchController.clear();
    });
    _loadData();
  }

  void _navigateToDetail(String idObyekLelang) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => IkanDetailScreen(idObyekLelang: idObyekLelang),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _startCountdown(IkanLelang ikan) {
    // Cancel existing timer if any
    _timers[ikan.idObyekLelang]?.cancel();

    final dateFormat = DateFormat("yyyy-MM-dd HH:mm:ss");
    final targetDate = dateFormat.parse(ikan.lastCloseTimer);
    final now = DateTime.now();
    final difference = targetDate.difference(now);
    final twoHours = const Duration(hours: 2);

    if (difference > Duration.zero && difference <= twoHours) {
      setState(() {
        _remainingTimes[ikan.idObyekLelang] = difference;
      });

      _timers[ikan.idObyekLelang] = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_remainingTimes[ikan.idObyekLelang]! > Duration.zero) {
          setState(() {
            _remainingTimes[ikan.idObyekLelang] = _remainingTimes[ikan.idObyekLelang]! - const Duration(seconds: 1);
          });
        } else {
          timer.cancel();
          _timers.remove(ikan.idObyekLelang);
          _remainingTimes.remove(ikan.idObyekLelang);
        }
      });
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              // Search field
              Expanded(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Cari ikan...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _onSearch("");
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    ),
                    onSubmitted: _onSearch,
                  ),
                ),
              ),

              // Sort button
              TextButton.icon(
                icon: const Icon(Icons.sort, color: Colors.white),
                label: const Text(
                  'Urutkan',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                style: TextButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                onPressed: _showSortDialog,
              ),
              const SizedBox(width: 8),

              // Filter button
              TextButton.icon(
                icon: const Icon(Icons.filter_list, color: Colors.white),
                label: const Text(
                  'Filter',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                style: TextButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                onPressed: _showFilterDialog,
              ),
            ],
          ),
        ),

        // Main content
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _ikanList.isEmpty
                  ? Center(child: Text(_searchQuery.isEmpty ? 'Tidak ada data ikan' : 'Ikan tidak ditemukan'))
                  : RefreshIndicator(
                      onRefresh: _loadData,
                      child: GridView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(4),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.75,
                          mainAxisSpacing: 4,
                          crossAxisSpacing: 4,
                        ),
                        itemCount: _ikanList.length + (_isLoadingMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _ikanList.length) {
                            return const Center(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: CircularProgressIndicator(),
                              ),
                            );
                          }

                          final ikan = _ikanList[index];
                          return _buildIkanCard(ikan);
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildIkanCard(IkanLelang ikan) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: InkWell(
        onTap: () => _navigateToDetail(ikan.idObyekLelang),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image and status
            Stack(
              children: [
                // Fish image
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Container(
                    color: AppColors.darkBlue,
                    child: CachedNetworkImage(
                      imageUrl: ikan.viewFoto,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(Icons.photo, color: Colors.white),
                      ),
                    ),
                  ),
                ),

                // KC badge and Mode Lelang
                if (int.parse(ikan.ikanKc) >= 1)
                  Positioned(
                    left: 8,
                    bottom: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.darkBlue,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        ikan.kodeLelang,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ),

                // Status badges and timer
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Status badge
                      if (ikan.viewStatusBid.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Color(int.parse(ikan.colorStatusBid.replaceAll('#', '0xFF'))),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            ikan.viewStatusBid,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),

                      // Timer
                      if (_remainingTimes.containsKey(ikan.idObyekLelang))
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.timer_outlined, size: 14, color: Colors.white),
                              const SizedBox(width: 4),
                              Text(
                                _formatDuration(_remainingTimes[ikan.idObyekLelang]!),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),

                // Fish location
                if (ikan.lokasiIkan.isNotEmpty)
                  Positioned(
                    left: 8,
                    bottom: int.parse(ikan.ikanKc) >= 1 ? 48 : 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        ikan.lokasiIkan,
                        style: TextStyle(
                          color: AppColors.darkBlue,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
              ],
            ),

            // Fish details
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Fish name
                  Text(
                    ikan.namaObyekLelang,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.darkBlue,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Bid info
                  if (ikan.opsiDeal == 0)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'OB',
                              style: TextStyle(fontSize: 12, color: AppColors.darkBlue),
                            ),
                            Text(
                              ikan.ob,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.darkBlue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'KB',
                              style: TextStyle(fontSize: 12, color: AppColors.darkBlue),
                            ),
                            Text(
                              ikan.kb,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.darkBlue,
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                      ],
                    )
                  else
                    Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'BIN',
                              style: TextStyle(fontSize: 12, color: AppColors.darkBlue),
                            ),
                            Text(
                              ikan.bin,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.darkBlue,
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                      ],
                    ),

                  const SizedBox(height: 4),

                  // Current bid status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (ikan.opsiDeal == 0)
                        Text(
                          '${ikan.jmlBid}x BID',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(int.parse(ikan.colorLastValueBid.replaceAll('#', '0xFF'))),
                          ),
                        ),
                      const Spacer(),
                      Text(
                        ikan.viewLastValueBid,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(int.parse(ikan.colorLastValueBid.replaceAll('#', '0xFF'))),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Seller info
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          ikan.namaMerchantSeller,
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
