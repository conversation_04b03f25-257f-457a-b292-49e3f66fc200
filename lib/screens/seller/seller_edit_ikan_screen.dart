import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import '../../models/ikan_lelang.dart';
import '../../providers/auth_provider.dart';
import '../../utils/colors.dart';

class SellerEditIkanScreen extends StatefulWidget {
  final String idObyekLelang;

  const SellerEditIkanScreen({
    Key? key,
    required this.idObyekLelang,
  }) : super(key: key);

  @override
  State<SellerEditIkanScreen> createState() => _SellerEditIkanScreenState();
}

class _SellerEditIkanScreenState extends State<SellerEditIkanScreen> {
  IkanLelang? ikan;
  bool isLoading = true;
  bool isLoadingSimpan = false;
  String messageAlert = '';
  bool showConfirmation = false;

  final _formKey = GlobalKey<FormState>();
  final _valueObController = TextEditingController();
  final _valueKbController = TextEditingController();
  final _binController = TextEditingController();
  final _keteranganController = TextEditingController();

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  @override
  void dispose() {
    _valueObController.dispose();
    _valueKbController.dispose();
    _binController.dispose();
    _keteranganController.dispose();
    super.dispose();
  }

  Future<void> fetchData() async {
    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .get('/api/ikan/${widget.idObyekLelang}');

      if (response != null && response['meta'] != null && response['meta']['code'] == 200) {
        final data = response['data'];
        if (data != null) {
          setState(() {
            try {
              ikan = IkanLelang.fromJson(data);
              _valueObController.text = data['value_ob']?.toString() ?? '';
              _valueKbController.text = data['value_kb']?.toString() ?? '';
              _binController.text = data['bin']?.toString() ?? '';
              _keteranganController.text = data['keterangan']?.toString() ?? '';
              messageAlert = '';
            } catch (e) {
              messageAlert = 'Error parsing data: ${e.toString()}';
            }
          });
        } else {
          setState(() {
            messageAlert = 'No data received from server';
          });
        }
      } else {
        setState(() {
          messageAlert = response?['meta']?['message'] ?? 'Unknown error occurred';
        });
      }
    } catch (e) {
      setState(() {
        messageAlert = 'Failed to connect to server: ${e.toString()}';
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString())),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  Future<void> _saveData() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => isLoadingSimpan = true);

    try {
      final data = {
        'value_ob': _valueObController.text,
        'value_kb': _valueKbController.text,
        'bin': _binController.text,
        'keterangan': _keteranganController.text,
      };

      final response = await Provider.of<AuthProvider>(context, listen: false)
          .post('/api/ikan/${widget.idObyekLelang}/edit', data);

      if (response['meta']['code'] == 200) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Data berhasil disimpan')),
        );
        Navigator.pop(context, true);
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response['meta']['message'] ?? 'Failed to save')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      if (mounted) {
        setState(() => isLoadingSimpan = false);
      }
    }
  }

  Future<void> _showFullImage(String imageUrl) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: PhotoView(
            imageProvider: NetworkImage(imageUrl),
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.covered * 2,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (ikan == null) {
      return Scaffold(
        body: Center(
          child: Text(messageAlert),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Ikan Lelang'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Fish Image and Preview
            Stack(
              children: [
                CachedNetworkImage(
                  imageUrl: ikan!.viewFoto,
                  height: 300,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
                if (int.parse(ikan!.ikanKc) > 0)
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      color: AppColors.primary,
                      child: Text(
                        ikan!.kodeLelangMode,
                        style: const TextStyle(
                          color: Colors.white,
                          fontStyle: FontStyle.italic,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Row(
                    children: [
                      FloatingActionButton(
                        heroTag: 'photo',
                        onPressed: () => _showFullImage(ikan!.viewFoto),
                        backgroundColor: Colors.red,
                        child: const Icon(Icons.photo),
                      ),
                      if (ikan!.viewVideo.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          heroTag: 'video',
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              '/video-player',
                              arguments: ikan!.viewVideo,
                            );
                          },
                          backgroundColor: Colors.red,
                          child: const Icon(Icons.play_arrow),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            // Fish Details Form
            Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Detail Ikan',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _valueObController,
                      decoration: const InputDecoration(
                        labelText: 'Open Bid',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'Open Bid harus diisi';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _valueKbController,
                      decoration: const InputDecoration(
                        labelText: 'Kelipatan Bid',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value?.isEmpty ?? true) {
                          return 'Kelipatan Bid harus diisi';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _binController,
                      decoration: const InputDecoration(
                        labelText: 'Buy It Now',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _keteranganController,
                      decoration: const InputDecoration(
                        labelText: 'Keterangan',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isLoadingSimpan ? null : _saveData,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(32),
                          ),
                        ),
                        child: isLoadingSimpan
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('Simpan'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
