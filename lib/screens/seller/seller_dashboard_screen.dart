import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/api_service.dart';
import '../../utils/colors.dart';
import '../../utils/preference_manager.dart';

/// SellerDashboardScreen - Complete seller analytics dashboard
/// Based on MasterKoiBot SellerDashboard.swift with enhanced features
class SellerDashboardScreen extends StatefulWidget {
  const SellerDashboardScreen({Key? key}) : super(key: key);

  @override
  State<SellerDashboardScreen> createState() => _SellerDashboardScreenState();
}

class _SellerDashboardScreenState extends State<SellerDashboardScreen> {
  bool _isLoading = true;
  bool _needLogin = false;

  // Dashboard data
  Map<String, dynamic> _dashboardData = {};
  Map<String, dynamic> _pointsData = {};

  // Profile data
  String _kodeSeller = '';
  String _namaMerchantSeller = '';
  String _viewSaldoDeposit = '';
  String _iconPointLevel = '';
  String _namaPointLevel = '';

  // Statistics
  String _jumlahIkanTerlelang = '0';
  String _jumlahIkanBelumTerlelang = '0';
  String _persenIkanTerlelang = '0';
  String _viewAvgNilaiIkanTerlelang = '0';
  String _jumlahIkanSudahDikirim = '0';
  String _jumlahIkanBelumDikirim = '0';
  String _jumlahIkanSudahDibayar = '0';
  String _jumlahIkanBelumDibayar = '0';

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // Load profile data from preferences
      final prefs = await SharedPreferences.getInstance();
      _kodeSeller = await prefs.getString('kode_seller') ?? '';
      _namaMerchantSeller = await prefs.getString('nama_merchant_seller') ?? '';

      // Load dashboard data from API
      final dashboardResponse = await ApiService().getSellerDashboard();
      final pointsResponse = await ApiService().getPoints();

      if (dashboardResponse['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
        return;
      }

      if (dashboardResponse['meta']['code'] == 200) {
        setState(() {
          _dashboardData = dashboardResponse['data'];
          _extractDashboardData();
        });
      }

      if (pointsResponse['statusCode'] == 200) {
        setState(() {
          _pointsData = pointsResponse['data'];
          _extractPointsData();
        });
      }

    } catch (e) {
      _showErrorDialog('Error loading dashboard: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _extractDashboardData() {
    _jumlahIkanTerlelang = _dashboardData['jumlah_ikan_terlelang']?.toString() ?? '0';
    _jumlahIkanBelumTerlelang = _dashboardData['jumlah_ikan_belum_terlelang']?.toString() ?? '0';
    _persenIkanTerlelang = _dashboardData['persen_ikan_terlelang']?.toString() ?? '0';
    _viewAvgNilaiIkanTerlelang = _dashboardData['view_avg_nilai_ikan_terlelang'] ?? '0';
    _jumlahIkanSudahDikirim = _dashboardData['jumlah_ikan_sudah_dikirim']?.toString() ?? '0';
    _jumlahIkanBelumDikirim = _dashboardData['jumlah_ikan_belum_dikirim']?.toString() ?? '0';
    _jumlahIkanSudahDibayar = _dashboardData['jumlah_ikan_sudah_dibayar']?.toString() ?? '0';
    _jumlahIkanBelumDibayar = _dashboardData['jumlah_ikan_belum_dibayar']?.toString() ?? '0';
    _viewSaldoDeposit = _dashboardData['view_saldo_deposit'] ?? '0';
  }

  void _extractPointsData() {
    _iconPointLevel = _pointsData['icon_point_level'] ?? '';
    _namaPointLevel = _pointsData['nama_point_level'] ?? '';
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Seller'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    _buildProfileHeader(),
                    const SizedBox(height: 24),

                    // Statistics Cards
                    _buildStatisticsCards(),
                    const SizedBox(height: 24),

                    // Charts Section
                    _buildChartsSection(),
                    const SizedBox(height: 24),

                    // Quick Actions
                    _buildQuickActions(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white,
                child: Text(
                  _kodeSeller.isNotEmpty ? _kodeSeller[0].toUpperCase() : 'S',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _kodeSeller,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _namaMerchantSeller,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(Icons.account_balance_wallet, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                'Deposit: $_viewSaldoDeposit',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          if (_iconPointLevel.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                // You can add network image here for point level icon
                const Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 8),
                Text(
                  _namaPointLevel,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Statistik Penjualan',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // First row of cards
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Ikan Terlelang',
                _jumlahIkanTerlelang,
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Belum Terlelang',
                _jumlahIkanBelumTerlelang,
                Icons.pending,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Second row of cards
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Persentase Terlelang',
                '$_persenIkanTerlelang%',
                Icons.percent,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Rata-rata Nilai',
                _viewAvgNilaiIkanTerlelang,
                Icons.attach_money,
                Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Third row of cards
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Sudah Dikirim',
                _jumlahIkanSudahDikirim,
                Icons.local_shipping,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Belum Dikirim',
                _jumlahIkanBelumDikirim,
                Icons.inventory,
                Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Analisis Visual',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Pie Chart for auction status
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLegendItem('Terlelang', Colors.green),
                    _buildLegendItem('Belum Terlelang', Colors.orange),
                    _buildLegendItem('Sudah Dikirim', Colors.blue),
                    _buildLegendItem('Belum Dikirim', Colors.red),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections() {
    final terlelang = int.tryParse(_jumlahIkanTerlelang) ?? 0;
    final belumTerlelang = int.tryParse(_jumlahIkanBelumTerlelang) ?? 0;
    final sudahDikirim = int.tryParse(_jumlahIkanSudahDikirim) ?? 0;
    final belumDikirim = int.tryParse(_jumlahIkanBelumDikirim) ?? 0;

    final total = terlelang + belumTerlelang + sudahDikirim + belumDikirim;
    if (total == 0) return [];

    return [
      PieChartSectionData(
        color: Colors.green,
        value: terlelang.toDouble(),
        title: '${(terlelang / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: belumTerlelang.toDouble(),
        title: '${(belumTerlelang / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.blue,
        value: sudahDikirim.toDouble(),
        title: '${(sudahDikirim / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.red,
        value: belumDikirim.toDouble(),
        title: '${(belumDikirim / total * 100).toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  Widget _buildLegendItem(String label, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Aksi Cepat',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildActionCard(
              'Tambah Ikan',
              Icons.add_circle,
              Colors.green,
              () {
                // Navigate to add fish screen
              },
            ),
            _buildActionCard(
              'Kelola Ikan',
              Icons.edit,
              Colors.blue,
              () {
                // Navigate to manage fish screen
              },
            ),
            _buildActionCard(
              'Laporan',
              Icons.analytics,
              Colors.purple,
              () {
                // Navigate to reports screen
              },
            ),
            _buildActionCard(
              'Pengaturan',
              Icons.settings,
              Colors.grey,
              () {
                // Navigate to settings screen
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}