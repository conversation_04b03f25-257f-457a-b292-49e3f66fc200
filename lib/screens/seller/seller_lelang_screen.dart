import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../providers/auth_provider.dart';
import '../../utils/colors.dart';
import '../../models/lelang.dart';
import '../../config/flavor_config.dart';
import '../../widgets/bidder_widgets.dart';

class SellerLelangScreen extends StatefulWidget {
  const SellerLelangScreen({Key? key}) : super(key: key);

  @override
  State<SellerLelangScreen> createState() => _SellerLelangScreenState();
}

class _SellerLelangScreenState extends State<SellerLelangScreen> {
  List<Lelang> lelangList = [];
  bool isLoading = false;
  String messageAlert = '';
  final prefs =  SharedPreferences.getInstance();
  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> fetchData() async {
    setState(() => isLoading = true);

    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .get('/api/seller/lelang');

      if (response['meta']['code'] == 200) {
        setState(() {
          messageAlert = '';
          lelangList = (response['data'] as List)
              .map((item) => Lelang.fromJson(item))
              .toList();
        });
      } else {
        setState(() {
          messageAlert = response['meta']['message'] ?? 'Unknown error occurred';
        });
      }
    } catch (e) {
      setState(() {
        messageAlert = 'Failed to connect to server';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _shareLelang(Lelang lelang) async {
    final text = '''
${lelang.listLelang}
${FlavorConfig.instance.baseUrl}/lelang/${lelang.idLelang}

Download app ${FlavorConfig.instance.appName} by neTurmeric di:
${FlavorConfig.instance.baseUrl}/app
''';
    await Share.share(text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lelang Seller'),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : lelangList.isEmpty
              ? Center(
                  child: Text(
                    messageAlert,
                    style: const TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: lelangList.length,
                  itemBuilder: (context, index) {
                    final lelang = lelangList[index];
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.all(4),
                      child: Column(
                        children: [
                          ListTile(

                            title: Text(lelang.listLelang),
                            subtitle: Text('${FlavorConfig.instance.baseUrl}/lelang/${lelang.idLelang}'),
                          ),
                          CachedNetworkImage(
                            imageUrl: lelang.viewFotoLelang,
                            height: 300,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                            errorWidget: (context, url, error) => const Icon(Icons.error),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    const Icon(Icons.access_time, size: 15, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Text(
                                      '- ${lelang.openTimer}',
                                      style: const TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                                Row(
                                  children: [
                                    const Icon(Icons.access_time, size: 15, color: Colors.grey),
                                    const SizedBox(width: 4),
                                    Text(
                                      '- ${lelang.closeTimer}',
                                      style: const TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => WebViewScreen(
                                            title: lelang.listLelang.toUpperCase(),
                                            url: '${FlavorConfig.instance.baseUrl}/lelang/${lelang.idLelang}',
                                          ),
                                        ),
                                      );
                                    },
                                    icon: const Icon(Icons.remove_red_eye),
                                    label: const Text('Lihat'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _shareLelang(lelang),
                                    icon: const Icon(Icons.share),
                                    label: const Text('Share'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.primary,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
    );
  }
}
