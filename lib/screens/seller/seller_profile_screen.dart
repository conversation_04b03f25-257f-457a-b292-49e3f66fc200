import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import 'package:photo_view/photo_view.dart';

class SellerProfileScreen extends StatefulWidget {
  const SellerProfileScreen({Key? key}) : super(key: key);

  @override
  State<SellerProfileScreen> createState() => _SellerProfileScreenState();
}

class _SellerProfileScreenState extends State<SellerProfileScreen> {
  // Form fields
  String namaMerchantSeller = "";
  String namaPemilikSeller = "";
  String alamatSeller = "";
  String kodePosSelector = "";
  String noHpSeller = "";
  String emailSeller = "";
  String kotaSeller = "";
  String nikSeller = "";
  String urlMapSeller = "";
  String bank = "";
  String nomorRekening = "";
  String atasNama = "";
  String noHpOperator = "";

  // Social media
  String facebook = "";
  String instagram = "";
  String youtube = "";
  String tiktok = "";
  String website = "";

  // Toggle settings
  bool rekeningPenjamin = false;
  bool sharingMember = false;
  bool autoChangeFp = false;
  bool kickBnr = false;
  bool blastGroup = false;

  // Images
  String viewUrlFotoLogo = "";
  String urlFotoKtp = "";

  // Group list
  List<Group> groups = [];

  bool isLoading = false;
  bool isLoadingSimpan = false;
  String errorMessage = "";

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _loadGroups();
  }

  Future<void> _loadProfile() async {
    setState(() => isLoading = true);

    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .get('/api/profil');

      if (response['meta']['code'] == 200) {
        final seller = response['data']['seller'];
        setState(() {
          namaMerchantSeller = seller['nama_merchant_seller'] ?? '';
          namaPemilikSeller = seller['nama_pemilik_seller'] ?? '';
          alamatSeller = seller['alamat_seller'] ?? '';
          kodePosSelector = seller['kode_pos_seller'] ?? '';
          noHpSeller = seller['no_hp_seller'] ?? '';
          emailSeller = seller['email_seller'] ?? '';
          kotaSeller = seller['kota_seller'] ?? '';
          nikSeller = seller['nik_seller'] ?? '';
          urlMapSeller = seller['url_map_seller'] ?? '';
          bank = seller['bank'] ?? '';
          nomorRekening = seller['nomor_rekening'] ?? '';
          atasNama = seller['atas_nama'] ?? '';
          noHpOperator = seller['no_hp_operator'] ?? '';
          facebook = seller['facebook'] ?? '';
          instagram = seller['instagram'] ?? '';
          youtube = seller['youtube'] ?? '';
          tiktok = seller['tiktok'] ?? '';
          website = seller['website'] ?? '';
          rekeningPenjamin = seller['rekening_penjamin'] == 1;
          sharingMember = seller['sharing_member'] == 1;
          autoChangeFp = seller['auto_change_fp'] == 1;
          kickBnr = seller['kick_bnr'] == 1;
          blastGroup = seller['blast_group'] == 1;
          viewUrlFotoLogo = seller['view_url_foto_logo'] ?? '';
          urlFotoKtp = seller['url_foto_ktp'] ?? '';
        });
      }
    } catch (e) {
      setState(() => errorMessage = e.toString());
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _loadGroups() async {
    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .get('/api/seller/group');

      if (response['meta']['code'] == 200) {
        setState(() {
          groups = (response['data'] as List)
              .map((item) => Group.fromJson(item))
              .toList();
        });
      }
    } catch (e) {
      setState(() => errorMessage = e.toString());
    }
  }

  Future<void> _saveProfile() async {
    setState(() => isLoadingSimpan = true);

    try {
      final Map<String, dynamic> data = {
        'rekening_penjamin': rekeningPenjamin ? 1 : 0,
        'sharing_member': sharingMember ? 1 : 0,
        'auto_change_fp': autoChangeFp ? 1 : 0,
        'kick_bnr': kickBnr ? 1 : 0,
        'blast_group': blastGroup ? 1 : 0,
        if (facebook.isNotEmpty) 'facebook': facebook,
        if (instagram.isNotEmpty) 'instagram': instagram,
        if (youtube.isNotEmpty) 'youtube': youtube,
        if (tiktok.isNotEmpty) 'tiktok': tiktok,
        if (website.isNotEmpty) 'website': website,
        if (alamatSeller.isNotEmpty) 'alamat_seller': alamatSeller,
        if (kodePosSelector.isNotEmpty) 'kode_pos_seller': kodePosSelector,
        if (kotaSeller.isNotEmpty) 'kota_seller': kotaSeller,
        if (urlMapSeller.isNotEmpty) 'url_map_seller': urlMapSeller,
        if (noHpOperator.isNotEmpty) 'no_hp_operator': noHpOperator,
        if (emailSeller.isNotEmpty) 'email_seller': emailSeller,
      };

      final response = await Provider.of<AuthProvider>(context, listen: false)
          .post('/api/seller/set', data);

      if (response['meta']['code'] == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profil berhasil diperbarui')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() => isLoadingSimpan = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil Seller'),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Logo and KTP Images
                  AspectRatio(
                    aspectRatio: 16/9,
                    child: Stack(
                      children: [
                        if (viewUrlFotoLogo.isNotEmpty)
                          Image.network(
                            viewUrlFotoLogo,
                            fit: BoxFit.cover,
                            width: double.infinity,
                          ),
                        if (urlFotoKtp.isNotEmpty)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: () => _showFullImage(urlFotoKtp),
                              child: Image.network(
                                urlFotoKtp,
                                width: 100,
                                height: 100,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Personal Info Section
                        _buildSection(
                          "Informasi Pribadi",
                          [
                            _buildTextField("Nama Merchant", namaMerchantSeller, enabled: false),
                            _buildTextField("Nama Pemilik", namaPemilikSeller, enabled: false),
                            _buildTextField("NIK", nikSeller, enabled: false),
                            _buildTextField("Email", emailSeller, enabled: false),
                            _buildTextField("No HP", noHpSeller, enabled: false),
                            _buildTextField("No HP Operator", noHpOperator),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Social Media Section
                        _buildSection(
                          "Social Media",
                          [
                            _buildTextField("Facebook", facebook, hint: "https://www.facebook.com/nama.profil"),
                            _buildTextField("Instagram", instagram, hint: "https://instagram.com/nama.profil"),
                            _buildTextField("Youtube", youtube, hint: "https://www.youtube.com/@nama.channel"),
                            _buildTextField("TikTok", tiktok, hint: "https://www.tiktok.com/@nama.profil"),
                            _buildTextField("Website", website, hint: "https://www.urlwebsite.com"),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Address Section
                        _buildSection(
                          "Alamat",
                          [
                            _buildTextField("Alamat", alamatSeller),
                            _buildTextField("Kode Pos", kodePosSelector),
                            _buildTextField("Kota", kotaSeller),
                            _buildTextField("URL Map", urlMapSeller),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Bank Section
                        _buildSection(
                          "Informasi Bank",
                          [
                            _buildTextField("Bank", bank, enabled: false),
                            _buildTextField("Nomor Rekening", nomorRekening, enabled: false),
                            _buildTextField("Atas Nama", atasNama, enabled: false),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Settings Section
                        _buildSection(
                          "Pengaturan",
                          [
                            _buildSwitchTile(
                              "Setuju menampilkan pilihan REKPEN (Rekening Penjamin)",
                              rekeningPenjamin,
                              (value) => setState(() => rekeningPenjamin = value),
                            ),
                            _buildSwitchTile(
                              "Setuju membagikan member aktif group kepada Seller lain",
                              sharingMember,
                              (value) => setState(() => sharingMember = value),
                            ),
                            _buildSwitchTile(
                              "Ijinkan BOT mengubah foto profil group menjadi Foto Kolase saat lelang dimulai",
                              autoChangeFp,
                              (value) => setState(() => autoChangeFp = value),
                            ),
                            _buildSwitchTile(
                              "Ijinkan BOT mengeluarkan pelaku BID and RUN (BNR) dari group",
                              kickBnr,
                              (value) => setState(() => kickBnr = value),
                            ),
                            _buildSwitchTile(
                              "Setujui Group WhatsApp/Telegram menerima informasi penting melalui pesan blast",
                              blastGroup,
                              (value) => setState(() => blastGroup = value),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Groups Section
                        if (groups.isNotEmpty) ...[
                          const Text(
                            "Daftar Group",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ...groups.map((group) => GroupItemWidget(
                            group: group,
                            onToggleVisibility: (value) => _updateGroupVisibility(group.id, value),
                          )),
                        ],

                        const SizedBox(height: 24),

                        // Save Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: isLoadingSimpan ? null : _saveProfile,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(32),
                              ),
                            ),
                            child: isLoadingSimpan
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Text('Simpan'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildTextField(String label, String value, {bool enabled = true, String? hint}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: TextField(
        controller: TextEditingController(text: value),
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget _buildSwitchTile(String title, bool value, Function(bool) onChanged) {
    return SwitchListTile(
      title: Text(title),
      value: value,
      onChanged: onChanged,
    );
  }

  void _showFullImage(String imageUrl) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: PhotoView(
            imageProvider: NetworkImage(imageUrl),
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.covered * 2,
          ),
        ),
      ),
    );
  }

  Future<void> _updateGroupVisibility(String groupId, bool visible) async {
    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .post('/api/seller/group/$groupId', {'tampil_di_live': visible ? "1" : "0"});

      if (response['meta']['code'] != 200) {
        throw Exception(response['meta']['message']);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    }
  }
}

class Group {
  final String id;
  final String sellerId;
  final String platformGroupId;
  final String kodeBidGroup;
  final String urutanBidGroup;
  final String namaBidGroup;
  final String namaSingkatBidGroup;
  final String linkBidGroup;
  final String shortLinkBidGroup;
  final String jmlMemberBidGroup;
  final String kodeTopic;
  final String updatedAtMemberBidGroup;
  bool tampilDiLive;
  final String inputer;
  final String inputTimer;
  final String aktif;

  Group({
    required this.id,
    required this.sellerId,
    required this.platformGroupId,
    required this.kodeBidGroup,
    required this.urutanBidGroup,
    required this.namaBidGroup,
    required this.namaSingkatBidGroup,
    required this.linkBidGroup,
    required this.shortLinkBidGroup,
    required this.jmlMemberBidGroup,
    required this.kodeTopic,
    required this.updatedAtMemberBidGroup,
    required this.tampilDiLive,
    required this.inputer,
    required this.inputTimer,
    required this.aktif,
  });

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['id_bid_group'] ?? '',
      sellerId: json['id_seller'] ?? '',
      platformGroupId: json['id_platform_group'] ?? '',
      kodeBidGroup: json['kode_bid_group'] ?? '',
      urutanBidGroup: json['urutan_bid_group'] ?? '',
      namaBidGroup: json['nama_bid_group'] ?? '',
      namaSingkatBidGroup: json['nama_singkat_bid_group'] ?? '',
      linkBidGroup: json['link_bid_group'] ?? '',
      shortLinkBidGroup: json['short_link_bid_group'] ?? '',
      jmlMemberBidGroup: json['jml_member_bid_group'] ?? '',
      kodeTopic: json['kode_topik'] ?? '',
      updatedAtMemberBidGroup: json['updated_at_member_bid_group'] ?? '',
      tampilDiLive: json['tampil_di_live'] == '1',
      inputer: json['inputer'] ?? '',
      inputTimer: json['input_timer'] ?? '',
      aktif: json['aktif'] ?? '',
    );
  }
}

class GroupItemWidget extends StatelessWidget {
  final Group group;
  final Function(bool) onToggleVisibility;

  const GroupItemWidget({
    Key? key,
    required this.group,
    required this.onToggleVisibility,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Image.asset(
              group.shortLinkBidGroup.contains('tele')
                  ? 'assets/images/img_social_tele.png'
                  : 'assets/images/img_social_wa.png',
              width: 50,
              height: 50,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(group.namaSingkatBidGroup),
                      const Text(' - '),
                      Text('${group.jmlMemberBidGroup} Anggota'),
                    ],
                  ),
                  SwitchListTile(
                    title: const Text('Tampilkan di Live Lelang'),
                    value: group.tampilDiLive,
                    onChanged: onToggleVisibility,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
