import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/member.dart';

class SellerMostAuctionScreen extends StatefulWidget {
  const SellerMostAuctionScreen({Key? key}) : super(key: key);

  @override
  State<SellerMostAuctionScreen> createState() => _SellerMostAuctionScreenState();
}

class _SellerMostAuctionScreenState extends State<SellerMostAuctionScreen> {
  List<MemberAktif> memberAktifList = [];
  bool isLoading = false;
  String messageAlert = '';

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> fetchData() async {
    setState(() => isLoading = true);

    try {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .get('/api/seller/member-most-auction');

      if (response['meta']['code'] == 200) {
        setState(() {
          messageAlert = '';
          memberAktifList = (response['data'] as List)
              .map((item) => MemberAktif.fromJson(item))
              .toList();
        });
      } else {
        setState(() {
          messageAlert = response['meta']['message'] ?? 'Unknown error occurred';
        });
      }
    } catch (e) {
      setState(() {
        messageAlert = 'Failed to connect to server';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _openWhatsApp(String phone) async {
    final url = 'https://api.whatsapp.com/send/?phone=$phone&type=phone_number&app_absent=0';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('WhatsApp tidak terinstall')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Member Most Auction'),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : memberAktifList.isEmpty
              ? Center(
                  child: Text(
                    messageAlert,
                    style: const TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: memberAktifList.length,
                  itemBuilder: (context, index) {
                    final member = memberAktifList[index];
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: InkWell(
                        onTap: () => _openWhatsApp(member.noHpBuyer),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/images/img_social_wa.png',
                                width: 50,
                                height: 50,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      member.namaBuyer,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(member.noHpBuyer),
                                    Text('${member.jmlBidGroup} Grup WhatsApp'),
                                    Row(
                                      children: [
                                        Text('${member.jmlBid}x Bid'),
                                        const SizedBox(width: 8),
                                        Text('Menang ${member.jmlObyekLelang} Ekor'),
                                      ],
                                    ),
                                    Text('AVG Rp ${member.viewAvgNilaiPerIkan} / Ekor'),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
