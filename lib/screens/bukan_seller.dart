import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/flavor_config.dart';
import 'auth/otp_verification_screen.dart';

class BukanSellerScreen extends StatefulWidget {
  const BukanSellerScreen({Key? key}) : super(key: key);

  @override
  State<BukanSellerScreen> createState() => _BukanSellerScreenState();
}

class _BukanSellerScreenState extends State<BukanSellerScreen> {
  bool isLoadingDaftar = false;

  Future<void> openWhatsapp() async {
    setState(() {
      isLoadingDaftar = true;
    });

    try {
      //final session = await SharedPreferencesHelper.getString('session');
     final session = "";
      final response = await http.get(
        Uri.parse('${FlavorConfig.instance.baseUrl}/api/seller/cs'),
        headers: {'Session': session ?? ''},
      );

      setState(() {
        isLoadingDaftar = false;
      });

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);

        if (json['meta']['action'] == 'login') {
          if (!mounted) return;
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OtpVerificationScreen(
                onSuccess: () {
                  Navigator.pop(context);
                },
              ),
            ),
          );
        } else if (json['meta']['code'] == 200) {
          final noCs = json['data'] as String;
          final whatsappUrl = 'https://api.whatsapp.com/send/?phone=$noCs&text=DAFTAR&type=phone_number&app_absent=0';

          if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
            await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.externalApplication);
          } else {
            if (!mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('WhatsApp tidak terinstall')),
            );
          }
        }
      }
    } catch (e) {
      setState(() {
        isLoadingDaftar = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.info_outline,
              size: 64,
              color: Colors.black54,
            ),
            const SizedBox(height: 16),
            const Text(
              'Menu Khusus Seller',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Maaf, Anda login bukan sebagai Seller. Untuk membuka menu ini, Anda harus mendaftar sebagai Seller dengan klik tombol Daftar Seller berikut.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[700],
                ),
              ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: ElevatedButton(
                onPressed: isLoadingDaftar ? null : openWhatsapp,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isLoadingDaftar)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    else ...[
                      const Icon(Icons.edit, size: 20),
                      const SizedBox(width: 8),
                      const Text('Daftar'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: const Text('Kembali'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
