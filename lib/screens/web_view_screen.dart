import 'dart:io';
import 'package:flutter/material.dart';
import 'package:masterkoi_app/config/flavor_config.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class WebViewScreen extends StatefulWidget {
  final String url;
  final String title;
  final bool isPdf;

  const WebViewScreen({
    Key? key,
    required this.url,
    required this.title,
    this.isPdf = false,
  }) : super(key: key);

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    if (Platform.isAndroid) {
      //WebView.platform = AndroidWebView();
    }
    _initWebViewController();
  }

  void _initWebViewController() {
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (String url) {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                });
              },
              onPageFinished: (String url) {
                setState(() {
                  _isLoading = false;
                });
              },
              onWebResourceError: (WebResourceError error) {
                setState(() {
                  _isLoading = false;
                  _hasError = true;
                });
              },
              onNavigationRequest: (NavigationRequest request) {
                final url = request.url;

                // Handle external URLs
                if (url.startsWith('tel:') ||
                    url.startsWith('whatsapp:') ||
                    url.startsWith('https://wa.me') ||
                    url.startsWith('https://chat.whatsapp.com') ||
                    url.startsWith('tg:') ||
                    url.startsWith('https://maps.app.goo.gl') ||
                    url.startsWith('https://play.google.com/store/apps/') ||
                    url.startsWith('https://facebook.com') ||
                    url.startsWith('https://www.facebook.com') ||
                    url.startsWith('https://instagram.com') ||
                    url.startsWith('https://www.instagram.com') ||
                    url.startsWith('https://youtube.com') ||
                    url.startsWith('https://www.youtube.com') ||
                    url.startsWith('https://www.tiktok.com') ||
                    url.startsWith('https://tiktok.com')) {
                  _launchExternalUrl(url);
                  return NavigationDecision.prevent;
                }

                // Handle internal URLs
                if (url.startsWith('${FlavorConfig.instance.baseUrl}/ikan/')) {
                  final parts = url.split('-');
                  final desiredValue = parts.last.replaceAll(
                    RegExp(r'[^0-9]'),
                    '',
                  );

                  // Navigate to IkanDetailActivity equivalent in Flutter
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder: (context) => IkanDetailScreen(idObyekLelang: desiredValue),
                  //   ),
                  // );

                  return NavigationDecision.prevent;
                }

                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(
            Uri.parse(
              widget.isPdf
                  ? 'https://docs.google.com/gview?embedded=true&url=${widget.url}'
                  : widget.url,
            ),
          );
  }

  Future<void> _launchExternalUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Tidak dapat membuka $url')));
    }
  }

  void _shareUrl() {
    final appPackageName =
        'com.masterkoi.bid'; // Replace with your app package name

    if (widget.isPdf) {
      Share.share(
        '${widget.url}\n\nDownload app ${FlavorConfig.instance.appName} by neTurmeric di: \nhttps://play.google.com/store/apps/details?id=$appPackageName',
      );
    } else {
      _controller.currentUrl().then((currentUrl) {
        if (currentUrl != null) {
          Share.share(
            '${widget.title}\n$currentUrl\n\nDownload app ${FlavorConfig.instance.appName} by neTurmeric di: \nhttps://play.google.com/store/apps/details?id=$appPackageName',
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.share), onPressed: _shareUrl),
        ],
        leading: IconButton( onPressed: () {
          Navigator.of(context).pop();
        }, icon: const Icon(Icons.arrow_back)
        ),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),

          if (_isLoading) const Center(child: CircularProgressIndicator()),

          if (_hasError)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/img_no_internet.png',
                    width: 250,
                    height: 250,
                  ),
                  const Text(
                    'Whoops!',
                    style: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      'Gagal terhubung dengan server, Periksa kembali koneksi internet anda.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _hasError = false;
                        _isLoading = true;
                      });
                      _controller.reload();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: const Text('Ulangi'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
