import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../providers/auth_provider.dart';
import '../../utils/colors.dart';


class OtpVerificationScreen extends StatefulWidget {
  final VoidCallback? onSuccess;
  final String? phoneNumber;

  const OtpVerificationScreen({
    Key? key,
    this.onSuccess,
    this.phoneNumber,
  }) : super(key: key);

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> _controllers = List.generate(
    5,
    (_) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(5, (_) => FocusNode());

  int _resendSeconds = 60;
  Timer? _timer;
  String _messageAlert = 'Kode OTP telah dikirim ke nomor WhatsApp Anda';

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendSeconds = 60;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendSeconds > 0) {
        setState(() {
          _resendSeconds--;
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  Future<void> _resendOtp() async {
    if (widget.phoneNumber == null || widget.phoneNumber!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Nomor telepon tidak ditemukan'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      // Use the same API endpoint as Swift
      final response = await authProvider.post('/api/login/request-otp', {
        'nomor_wa': widget.phoneNumber!,
      });

      if (mounted) {
        final meta = response['meta'];
        final kodeAlert = meta['code'] ?? 0;
        final messageAlert = meta['message'] ?? "Unknown error";

        if (meta['action'] == 'login') {
          // Handle login redirect if needed
        } else if (kodeAlert == 200 || kodeAlert == 406) {
          setState(() {
            _messageAlert = messageAlert;
          });

          _startResendTimer();

          for (var controller in _controllers) {
            controller.clear();
          }
          _focusNodes[0].requestFocus();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(messageAlert),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(messageAlert),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Gagal melakukan koneksi ke server'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _verifyOtp() async {
    try {
      final otp = _controllers.map((c) => c.text).join();

      if (otp.length != 5) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Masukkan 5 digit kode OTP'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Use the same API endpoint as Swift
      final response = await authProvider.post('/api/login/with-otp', {
        'nomor_wa': widget.phoneNumber ?? '',
        'kode_otp': otp,
      });

      if (mounted) {
        final meta = response['meta'];
        final kodeAlert = meta['code'] ?? 0;
        final messageAlert = meta['message'] ?? "Unknown error";

        if (meta['action'] == 'login') {
          // Handle login redirect if needed
        } else if (kodeAlert == 200 || kodeAlert == 406) {
          // Success - save session and user data like Swift
          final data = response['data'];
          final session = data['session'];

          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('session', session);

          // Save user data like Swift
          final refUsers = data['ref_users'];
          await prefs.setString('view_nama', refUsers['view_nama'] ?? '');
          await prefs.setString('no_wa', refUsers['no_wa'] ?? '');

          // Save seller data if available
          if (data['seller'] != null) {
            final seller = data['seller'];
            await prefs.setString('view_url_foto_logo', seller['view_url_foto_logo'] ?? '');
            await prefs.setString('list_seller', seller['list_seller'] ?? '');
            await prefs.setString('nama_pemilik_seller', seller['nama_pemilik_seller'] ?? '');
          }

          // Save buyer data if available
          if (data['buyer'] != null) {
            final buyer = data['buyer'];
            await prefs.setString('nama_buyer', buyer['nama_buyer'] ?? '');
            await prefs.setString('list_buyer', buyer['list_buyer'] ?? '');
            await prefs.setString('no_hp_buyer', buyer['no_hp_buyer'] ?? '');
            await prefs.setString('as_bnr', buyer['as_bnr'] ?? '');
            await prefs.setString('nomor_bantuan', buyer['nomor_bantuan'] ?? '');
          }

          widget.onSuccess?.call();
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(messageAlert),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal melakukan koneksi ke server'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verifikasi OTP'),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Image like Swift
                    Image.asset(
                      'assets/images/img_number_verification.png',
                      width: 160,
                      height: 160,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.message_outlined,
                          size: 160,
                          color: AppColors.primary,
                        );
                      },
                    ),

                    const SizedBox(height: 24),

                    const Text(
                      'Login dengan nomor WhatsApp Anda',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    Text(
                      _messageAlert,
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 40),

                    // OTP Input like Swift
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(5, (index) {
                        return SizedBox(
                          width: 48,
                          height: 48,
                          child: TextField(
                            controller: _controllers[index],
                            focusNode: _focusNodes[index],
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            maxLength: 1,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              contentPadding: EdgeInsets.zero,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5),
                                borderSide: BorderSide(
                                  color: Colors.grey.shade300,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5),
                                borderSide: BorderSide(
                                  color: AppColors.primary,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade100,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            onChanged: (value) {
                              if (value.isNotEmpty) {
                                if (index < 4) {
                                  _focusNodes[index + 1].requestFocus();
                                } else {
                                  // Auto verify when last digit is entered
                                  _verifyOtp();
                                }
                              } else if (index > 0) {
                                _focusNodes[index - 1].requestFocus();
                              }
                            },
                          ),
                        );
                      }),
                    ),

                    const SizedBox(height: 40),

                    // Verify Button like Swift
                    ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _verifyOtp,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: authProvider.isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.check, color: Colors.white),
                                SizedBox(width: 8),
                                Text(
                                  'Verifikasi',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                    ),

                    const SizedBox(height: 16),

                    // Back Button like Swift
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.chevron_left, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Kembali',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('Tidak menerima kode? '),
                        _resendSeconds > 0
                            ? Text(
                                'Kirim ulang dalam $_resendSeconds detik',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : TextButton(
                                onPressed: _resendOtp,
                                child: Text(
                                  'Kirim Ulang',
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ],
                    ),

                    if (authProvider.errorMessage.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(top: 16),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Text(
                          authProvider.errorMessage,
                          style: TextStyle(color: Colors.red.shade800),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
