import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../providers/auth_provider.dart';
import '../../utils/colors.dart';


class PhoneInputScreen extends StatefulWidget {
  final VoidCallback? onSuccess;

  const PhoneInputScreen({
    Key? key,
    this.onSuccess,
  }) : super(key: key);

  @override
  State<PhoneInputScreen> createState() => _PhoneInputScreenState();
}

class _PhoneInputScreenState extends State<PhoneInputScreen> with TickerProviderStateMixin {
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();

  // OTP Controllers
  final List<TextEditingController> _otpControllers = List.generate(5, (_) => TextEditingController());
  final List<FocusNode> _otpFocusNodes = List.generate(5, (_) => FocusNode());

  // State management like Swift
  bool _setelahKirimOTP = false;
  String _messageAlert = "Ketik nomor WhatsApp Anda untuk mendapatkan kode verifikasi.";
  bool _showingAlert = false;
  int _kodeAlert = 0;
  int _type = 0;

  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Auto focus pada field nomor telepon
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_setelahKirimOTP) {
        _phoneFocusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _phoneFocusNode.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _otpFocusNodes) {
      node.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  // Validation like Swift - minimum 5 characters
  bool _validatePhoneNumber() {
    if (_phoneController.text.length < 5) {
      setState(() {
        _messageAlert = "Pastikan No Whatsapp diisi dengan benar";
        _showingAlert = true;
      });
      return false;
    }
    return true;
  }

  // Request OTP like Swift implementation
  Future<void> _requestOtp() async {
    if (!_validatePhoneNumber()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      final response = await authProvider.post('/api/login/request-otp', {
        'nomor_wa': _phoneController.text,
      });

      if (mounted) {
        final meta = response['meta'];
        _kodeAlert = meta['code'] ?? 0;
        _messageAlert = meta['message'] ?? "Unknown error";

        if (meta['action'] == 'login') {
          // Handle login redirect if needed
        } else if (_kodeAlert == 200 || _kodeAlert == 406) {
          // Success - switch to OTP input
          setState(() {
            _setelahKirimOTP = true;
            _messageAlert = meta['message'] ?? "Kode OTP telah dikirim";
          });

          // Save phone number
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('nomor_wa', _phoneController.text);

          // Animate transition
          _animationController.forward();

          // Focus first OTP field
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _otpFocusNodes[0].requestFocus();
          });
        } else {
          setState(() {
            _showingAlert = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _messageAlert = "Gagal melakukan koneksi ke server";
          _showingAlert = true;
        });
      }
    }
  }

  // Verify OTP like Swift implementation
  Future<void> _verifyOtp() async {
    final otp = _otpControllers.map((c) => c.text).join();

    if (otp.length != 5) {
      setState(() {
        _messageAlert = "Masukkan 5 digit kode OTP";
        _showingAlert = true;
      });
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    try {
      final response = await authProvider.post('/api/login/with-otp', {
        'nomor_wa': _phoneController.text,
        'kode_otp': otp,
      });

      if (mounted) {
        final meta = response['meta'];
        _kodeAlert = meta['code'] ?? 0;
        _messageAlert = meta['message'] ?? "Unknown error";

        if (meta['action'] == 'login') {
          // Handle login redirect if needed
        } else if (_kodeAlert == 200 || _kodeAlert == 406) {
          // Success - save session and user data like Swift
          final data = response['data'];
          final session = data['session'];

          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('session', session);

          // Save user data like Swift
          final refUsers = data['ref_users'];
          await prefs.setString('view_nama', refUsers['view_nama'] ?? '');
          await prefs.setString('no_wa', refUsers['no_wa'] ?? '');

          // Save seller data if available
          if (data['seller'] != null) {
            final seller = data['seller'];
            await prefs.setString('view_url_foto_logo', seller['view_url_foto_logo'] ?? '');
            await prefs.setString('list_seller', seller['list_seller'] ?? '');
            await prefs.setString('nama_pemilik_seller', seller['nama_pemilik_seller'] ?? '');
          }

          // Save buyer data if available
          if (data['buyer'] != null) {
            final buyer = data['buyer'];
            await prefs.setString('nama_buyer', buyer['nama_buyer'] ?? '');
            await prefs.setString('list_buyer', buyer['list_buyer'] ?? '');
            await prefs.setString('no_hp_buyer', buyer['no_hp_buyer'] ?? '');
            await prefs.setString('as_bnr', buyer['as_bnr'] ?? '');
            await prefs.setString('nomor_bantuan', buyer['nomor_bantuan'] ?? '');
          }

          // Save FCM token
          await _saveFcmToken();

          setState(() {
            _type = 1;
            _showingAlert = true;
          });

          // Call success callback
          widget.onSuccess?.call();
        } else {
          setState(() {
            _showingAlert = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _messageAlert = "Gagal melakukan koneksi ke server";
          _showingAlert = true;
        });
      }
    }
  }

  // Save FCM token like Swift simpanFCM() implementation
  Future<void> _saveFcmToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = prefs.getString('session') ?? '';
      final fcmToken = prefs.getString('fcmToken') ?? '';

      // Only save if we have both session and FCM token
      if (session.length > 4 && fcmToken.isNotEmpty) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Use the exact same endpoint and parameter as Swift
        final response = await authProvider.postDirect('/api/profil/set', {
          'token_fcm': fcmToken, // Same parameter name as Swift
        });

        if (mounted) {
          final meta = response['meta'];
          final kodeAlert = meta['code'] ?? 0;

          if (meta['action'] == 'login') {
            // Session expired, handle re-login
            await prefs.remove('session');
          } else if (kodeAlert == 200 || kodeAlert == 406) {
            // Success - FCM token saved
            debugPrint('FCM token saved successfully: ${meta['message']}');
          } else {
            // Error saving FCM token
            debugPrint('Failed to save FCM token: ${meta['message']}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login'),
        elevation: 0,
        backgroundColor: AppColors.primary,
        leading: IconButton( onPressed: () {
          Navigator.of(context).pop();
        }, icon: const Icon(Icons.arrow_back)
        ),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints.maxHeight,
                    ),
                    child: IntrinsicHeight(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                    const SizedBox(height: 20),

                    // Image - changes based on state like Swift
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 500),
                      child: _setelahKirimOTP
                          ? Image.asset(
                              'assets/images/img_number_verification.png',
                              width: 160,
                              height: 160,
                              key: const ValueKey('number_verification'),
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.message_outlined,
                                  size: 160,
                                  color: AppColors.primary,
                                );
                              },
                            )
                          : Image.asset(
                              'assets/images/img_code_verification.png',
                              width: 160,
                              height: 160,
                              key: const ValueKey('code_verification'),
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.phone_android,
                                  size: 160,
                                  color: AppColors.primary,
                                );
                              },
                            ),
                    ),

                    const SizedBox(height: 24),

                    // Title
                    const Text(
                      'Login dengan nomor WhatsApp Anda',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // Dynamic message like Swift
                    Text(
                      _messageAlert,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    const SizedBox(height: 40),

                    // Privacy policy text like Swift
                    const Text(
                      'Dengan mendaftarkan nomor whatsapp, Anda menyetujui Ketentuan Layanan kami dan menyatakan bahwa Anda telah membaca Kebijakan Privasi kami.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    // Input section - changes based on state
                    if (_setelahKirimOTP) ...[
                      // OTP Input like Swift
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(5, (index) {
                          return SizedBox(
                            width: 48,
                            height: 48,
                            child: TextField(
                              controller: _otpControllers[index],
                              focusNode: _otpFocusNodes[index],
                              textAlign: TextAlign.center,
                              keyboardType: TextInputType.number,
                              maxLength: 1,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              decoration: InputDecoration(
                                counterText: '',
                                contentPadding: EdgeInsets.zero,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5),
                                  borderSide: BorderSide(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5),
                                  borderSide: BorderSide(
                                    color: AppColors.primary,
                                    width: 2,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade100,
                              ),
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              onChanged: (value) {
                                if (value.isNotEmpty) {
                                  if (index < 4) {
                                    _otpFocusNodes[index + 1].requestFocus();
                                  } else {
                                    // Auto verify when last digit is entered
                                    _verifyOtp();
                                  }
                                } else if (index > 0) {
                                  _otpFocusNodes[index - 1].requestFocus();
                                }
                              },
                            ),
                          );
                        }),
                      ),
                    ] else ...[
                      // Phone Input like Swift
                      TextField(
                        controller: _phoneController,
                        focusNode: _phoneFocusNode,
                        keyboardType: TextInputType.phone,
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          hintText: 'Nomor WhatsApp',
                          contentPadding: const EdgeInsets.all(16),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: AppColors.primary,
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.grey.shade100,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onSubmitted: (_) => _requestOtp(),
                      ),
                    ],

                    const SizedBox(height: 20),

                    const SizedBox(height: 40),

                    // Action Button like Swift
                    ElevatedButton(
                      onPressed: authProvider.isLoading
                          ? null
                          : (_setelahKirimOTP ? _verifyOtp : _requestOtp),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: authProvider.isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.check),
                                const SizedBox(width: 8),
                                Text(
                                  _setelahKirimOTP ? 'Verifikasi' : 'Lanjutkan',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                    ),

                    const SizedBox(height: 16),

                    // Back Button like Swift
                    ElevatedButton(
                      onPressed: () {
                        if (_setelahKirimOTP) {
                          // Go back to phone input
                          setState(() {
                            _setelahKirimOTP = false;
                            _messageAlert = "Ketik nomor WhatsApp Anda untuk mendapatkan kode verifikasi.";
                          });
                          _animationController.reverse();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        minimumSize: const Size.fromHeight(48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.chevron_left, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Kembali',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Alert overlay like Swift
                    if (_showingAlert)
                      Container(
                        margin: const EdgeInsets.only(top: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: _kodeAlert == 200 || _kodeAlert == 406
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _kodeAlert == 200 || _kodeAlert == 406
                                ? Colors.green.shade200
                                : Colors.red.shade200,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              _messageAlert,
                              style: TextStyle(
                                color: _kodeAlert == 200 || _kodeAlert == 406
                                    ? Colors.green.shade800
                                    : Colors.red.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _showingAlert = false;
                                });
                                if (_type == 1) {
                                  Navigator.of(context).pop();
                                }
                              },
                              child: const Text('OK'),
                            ),
                          ],
                        ),
                      ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
