import 'package:flutter/material.dart';

import '../../services/api_service.dart';
import '../auth/otp_verification_screen.dart';

class BidderProfileScreen extends StatefulWidget {
  const BidderProfileScreen({Key? key}) : super(key: key);

  @override
  State<BidderProfileScreen> createState() => _BidderProfileScreenState();
}

class _BidderProfileScreenState extends State<BidderProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isLoadingSave = false;
  bool _needLogin = false;

  Map<String, dynamic> _originalData = {};

  // Form fields
  final TextEditingController _namaBuyerController = TextEditingController();
  final TextEditingController _kodeBuyerController = TextEditingController();
  final TextEditingController _linkBuyerController = TextEditingController();
  final TextEditingController _alamatBuyerController = TextEditingController();
  final TextEditingController _kodePosBuyerController = TextEditingController();
  final TextEditingController _kotaBuyerController = TextEditingController();
  final TextEditingController _bankController = TextEditingController();
  final TextEditingController _noRekeningController = TextEditingController();
  final TextEditingController _atasNamaController = TextEditingController();
  bool _isBlastEnabled = false;

  @override
  void initState() {
    super.initState();
    _getProfil();
  }

  Future<void> _getProfil() async {
    setState(() => _isLoading = true);
    try {
      final response = await ApiService().get('/profil');

      if (response['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
      } else if (response['meta']['code'] == 200) {
        final buyerData = response['data']['buyer'];
        _originalData = buyerData;

        setState(() {
          _namaBuyerController.text = buyerData['nama_buyer'] ?? '';
          _kodeBuyerController.text = buyerData['kode_buyer'] ?? '';
          _linkBuyerController.text = buyerData['link_buyer'] ?? '';
          _alamatBuyerController.text = buyerData['alamat_buyer'] ?? '';
          _kodePosBuyerController.text = buyerData['kode_pos_buyer'] ?? '';
          _kotaBuyerController.text = buyerData['kota_buyer'] ?? '';
          _bankController.text = buyerData['bank'] ?? '';
          _noRekeningController.text = buyerData['nomor_rekening'] ?? '';
          _atasNamaController.text = buyerData['atas_nama'] ?? '';
          _isBlastEnabled = buyerData['terima_blast'] == 1;
        });
      }
    } catch (e) {
      // Handle error silently or show snackbar
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoadingSave = true);

    Map<String, dynamic> changes = {};

    // Only include fields that have changed
    if (_alamatBuyerController.text != _originalData['alamat_buyer']) {
      changes['alamat_buyer'] = _alamatBuyerController.text;
    }
    if (_kodePosBuyerController.text != _originalData['kode_pos_buyer']) {
      changes['kode_pos_buyer'] = _kodePosBuyerController.text;
    }
    if (_kotaBuyerController.text != _originalData['kota_buyer']) {
      changes['kota_buyer'] = _kotaBuyerController.text;
    }
    if (_bankController.text != _originalData['bank']) {
      changes['bank'] = _bankController.text;
    }
    if (_noRekeningController.text != _originalData['nomor_rekening']) {
      changes['nomor_rekening'] = _noRekeningController.text;
    }
    if (_atasNamaController.text != _originalData['atas_nama']) {
      changes['atas_nama'] = _atasNamaController.text;
    }

    changes['terima_blast'] = _isBlastEnabled ? 1 : 0;

    try {
      final response = await ApiService().post('/bidder/set', body: changes);

      if (response['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
      } else if (response['meta']['code'] == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response['meta']['message'])),
        );
        _getProfil(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(response['meta']['message'])),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save profile: \${e.toString()}')),
      );
    } finally {
      setState(() => _isLoadingSave = false);
    }
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          enabled: enabled,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_needLogin) {
      return const OtpVerificationScreen();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil Bidder'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTextField(
                      label: 'Nama',
                      controller: _namaBuyerController,
                      enabled: false,
                    ),
                    _buildTextField(
                      label: 'Kode',
                      controller: _kodeBuyerController,
                      enabled: false,
                    ),
                    _buildTextField(
                      label: 'Link Buyer',
                      controller: _linkBuyerController,
                      enabled: false,
                    ),
                    _buildTextField(
                      label: 'Alamat',
                      controller: _alamatBuyerController,
                    ),
                    _buildTextField(
                      label: 'Kode Pos',
                      controller: _kodePosBuyerController,
                    ),
                    _buildTextField(
                      label: 'Kota',
                      controller: _kotaBuyerController,
                    ),
                    _buildTextField(
                      label: 'Bank',
                      controller: _bankController,
                    ),
                    _buildTextField(
                      label: 'No Rekening',
                      controller: _noRekeningController,
                    ),
                    _buildTextField(
                      label: 'Atas Nama',
                      controller: _atasNamaController,
                    ),
                    SwitchListTile(
                      title: const Text(
                        'Setuju berlangganan menerima pesan blast',
                        style: TextStyle(fontSize: 14),
                      ),
                      value: _isBlastEnabled,
                      onChanged: (bool value) {
                        setState(() => _isBlastEnabled = value);
                      },
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoadingSave ? null : _saveProfile,
                        icon: _isLoadingSave
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.save),
                        label: const Text('Simpan'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.all(16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(32),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  @override
  void dispose() {
    _namaBuyerController.dispose();
    _kodeBuyerController.dispose();
    _linkBuyerController.dispose();
    _alamatBuyerController.dispose();
    _kodePosBuyerController.dispose();
    _kotaBuyerController.dispose();
    _bankController.dispose();
    _noRekeningController.dispose();
    _atasNamaController.dispose();
    super.dispose();
  }
}
