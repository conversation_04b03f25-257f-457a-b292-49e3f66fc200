import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../../models/history_bid.dart';
import '../../services/api_service.dart';
import '../auth/otp_verification_screen.dart';
import '../ikan_lelang/ikan_detail_screen.dart';

class BidderHistoryScreen extends StatefulWidget {
  const BidderHistoryScreen({Key? key}) : super(key: key);

  @override
  State<BidderHistoryScreen> createState() => _BidderHistoryScreenState();
}

class _BidderHistoryScreenState extends State<BidderHistoryScreen> {
  bool _isLoading = false;
  String _messageAlert = '';
  List<HistoryBid> _dataItemList = [];
  bool _needLogin = false;

  @override
  void initState() {
    super.initState();
    _getHistoryBids('');
  }

  Future<void> _getHistoryBids(String query) async {
    setState(() {
      _isLoading = true;
      _dataItemList = [];
    });

    try {
      final response = await ApiService().get('/bidder/history-bid');

      if (response.containsKey('meta')) {
        if (response['meta']['action'] == 'login') {
          setState(() => _needLogin = true);
        } else if (response['meta']['code'] == 200) {
          final items = response['data'] as List;
          setState(() {
            _messageAlert = '';
            _dataItemList = items.map((item) => HistoryBid.fromJson(item)).toList();
          });
        } else {
          setState(() => _messageAlert = response['meta']['message'] ?? '');
        }
      }
    } catch (e) {
      setState(() => _messageAlert = 'Failed to load history: \${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_needLogin) {
      return const OtpVerificationScreen();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Histori Bid'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _dataItemList.isEmpty
              ? Center(
                  child: Text(
                    _messageAlert,
                    style: const TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                )
              : ListView.builder(
                  itemCount: _dataItemList.length,
                  itemBuilder: (context, index) {
                    final item = _dataItemList[index];
                    return HistoryBidCard(item: item);
                  },
                ),
    );
  }
}

class HistoryBidCard extends StatelessWidget {
  final HistoryBid item;

  const HistoryBidCard({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => IkanDetailScreen(
              idObyekLelang: item.idObyekLelang
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  item.viewFoto,
                  width: 70,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 70,
                    height: 80,
                    color: Colors.grey[300],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Html(
                  data: item.viewIkanTransaksiBid,
                  style: {
                    "body": Style(
                      fontSize: FontSize(14),
                      margin: Margins.zero,
                      padding: HtmlPaddings.zero,
                    ),
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
