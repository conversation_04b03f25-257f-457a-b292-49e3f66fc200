import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/ikan_lelang.dart';
import '../../providers/wishlist_provider.dart';
import '../../services/api_service.dart';
import '../ikan_lelang/ikan_detail_screen.dart';

/// BidderWishlistScreen - Single file approach
/// Converted from Swift BidderWishlist.swift with enhanced functionality
/// Includes IkanLelangGridItem widget and all chat functions
class BidderWishlistScreen extends StatefulWidget {
  const BidderWishlistScreen({super.key});

  @override
  State<BidderWishlistScreen> createState() => _BidderWishlistScreenState();
}

class _BidderWishlistScreenState extends State<BidderWishlistScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  String _messageAlert = '';
  List<IkanLelang> _dataItemList = [];
  bool _needLogin = false;
  int _page = 1;
  final String _searchText = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final wishlistProvider = Provider.of<WishlistProvider>(
        context,
        listen: false,
      );
      wishlistProvider.loadWishlist();
      _getWishlistData(_searchText);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _page++;
      _getWishlistData(_searchText);
    }
  }

  /// Main function to get wishlist data from API
  /// Equivalent to getIkanLelang in Swift version
  Future<void> _getWishlistData(String query) async {
    if (_page == 1) {
      setState(() {
        _dataItemList = [];
        _isLoading = true;
      });
    }

    final wishlistProvider = Provider.of<WishlistProvider>(
      context,
      listen: false,
    );
    final wishlistIds = wishlistProvider.getWishlistAsString();

    if (wishlistIds.isEmpty) {
      setState(() {
        _isLoading = false;
        _messageAlert = 'Tidak ada ikan dalam wishlist';
      });
      return;
    }

    try {
      final response = await ApiService().get(
        '/api/ikan/check-live/$wishlistIds',
        queryParameters: {
          'page': _page.toString(),
          'search': query.isNotEmpty ? query : wishlistIds,
        },
      );

      if (response['meta']['action'] == 'login') {
        setState(() => _needLogin = true);
      } else if (response['meta']['code'] == 200) {
        final items = response['data'] as List;
        setState(() {
          _messageAlert = '';
          for (var item in items) {
            _dataItemList.add(IkanLelang.fromJson(item));
            wishlistProvider.addToWishlist(item['id_obyek_lelang'] ?? '');
          }
        });
      } else {
        setState(() => _messageAlert = response['meta']['message'] ?? '');
      }
    } catch (e) {
      setState(() => _messageAlert = 'Gagal memuat wishlist: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Refresh data function equivalent to refreshData in Swift
  Future<void> _refreshData() async {
    _page = 1;
    await _getWishlistData(_searchText);
  }

  /// Show dialog info function equivalent to showDialogInfo in Swift
  void _showDialogInfo(String title, String message, int type) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Chat with seller function equivalent to ChatSeller in Swift
  Future<void> _chatSeller(IkanLelang ikan) async {
    final message =
        'Hai Admin ${ikan.namaMerchantSeller}, '
        'saya mau tanya ikan ini : \n\n'
        '${ikan.namaObyekLelang}\n'
        '${ikan.shortLinkObyekLelang}';

    final url = Uri.parse(
      'https://api.whatsapp.com/send/?phone=${ikan.noHpSeller}'
      '&text=${Uri.encodeComponent(message)}'
      '&type=phone_number&app_absent=0',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        _showDialogInfo(
          'Informasi',
          'WhatsApp tidak terinstall di device ini.',
          1,
        );
      }
    }
  }

  /// Chat with support function equivalent to ChatBantuan in Swift
  Future<void> _chatBantuan(IkanLelang ikan) async {
    final message =
        'Hai Pusat Bantuan, saya mohon bantuan terkait ikan ini : \n\n'
        '${ikan.namaObyekLelang}\n'
        '${ikan.shortLinkObyekLelang}';

    final url = Uri.parse(
      'https://api.whatsapp.com/send/?phone=${ikan.noHpMarketing}'
      '&text=${Uri.encodeComponent(message)}'
      '&type=phone_number&app_absent=0',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        _showDialogInfo(
          'Informasi',
          'WhatsApp tidak terinstall di device ini.',
          1,
        );
      }
    }
  }

  /// Chat with winner function equivalent to ChatWinner in Swift
  Future<void> _chatWinner(IkanLelang ikan) async {
    final message =
        'Hai ${ikan.namaBuyer}, saya ingin menindaklanjuti ikan ini : \n\n'
        '${ikan.namaObyekLelang}\n'
        '${ikan.shortLinkObyekLelang}\n yang telah Anda menangkan';

    final url = Uri.parse(
      'https://api.whatsapp.com/send/?phone=${ikan.noHpBuyer}'
      '&text=${Uri.encodeComponent(message)}'
      '&type=phone_number&app_absent=0',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        _showDialogInfo(
          'Informasi',
          'WhatsApp tidak terinstall di device ini.',
          1,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Wishlist'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child:
            _isLoading && _page == 1
                ? const Center(child: CircularProgressIndicator())
                : _dataItemList.isEmpty
                ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      _messageAlert.isEmpty
                          ? 'Tidak ada ikan dalam wishlist'
                          : _messageAlert,
                      style: const TextStyle(color: Colors.grey, fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
                : GridView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    mainAxisSpacing: 8,
                    crossAxisSpacing: 8,
                  ),
                  itemCount: _dataItemList.length + (_isLoading ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _dataItemList.length) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final item = _dataItemList[index];
                    return IkanLelangGridItem(
                      item: item,
                      halaman: 'Wishlist',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => IkanDetailScreen(
                                  idObyekLelang: item.idObyekLelang,
                                ),
                          ),
                        );
                      },
                      onChatSeller: () => _chatSeller(item),
                      onChatBantuan: () => _chatBantuan(item),
                      onChatWinner: () => _chatWinner(item),
                    );
                  },
                ),
      ),
    );
  }
}

/// IkanLelangGridItem widget - Single file approach
/// Equivalent to IkanLelangGridItem in Swift version
class IkanLelangGridItem extends StatelessWidget {
  final IkanLelang item;
  final String halaman;
  final VoidCallback? onTap;
  final VoidCallback? onChatSeller;
  final VoidCallback? onChatBantuan;
  final VoidCallback? onChatWinner;

  const IkanLelangGridItem({
    super.key,
    required this.item,
    required this.halaman,
    this.onTap,
    this.onChatSeller,
    this.onChatBantuan,
    this.onChatWinner,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(8),
                  ),
                  child:
                      item.viewFoto.isNotEmpty
                          ? CachedNetworkImage(
                            imageUrl: item.viewFoto,
                            fit: BoxFit.cover,
                            placeholder:
                                (context, url) => Container(
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  color: Colors.grey[300],
                                  child: const Icon(
                                    Icons.error,
                                    color: Colors.red,
                                  ),
                                ),
                          )
                          : Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                ),
              ),
            ),
            // Content section
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      item.namaObyekLelang,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Price info
                    if (item.viewLastValueBid.isNotEmpty)
                      Text(
                        'Bid: ${item.viewLastValueBid}',
                        style: TextStyle(
                          color:
                              item.colorLastValueBid.isNotEmpty
                                  ? _getColorFromString(item.colorLastValueBid)
                                  : Colors.green,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    const SizedBox(height: 4),
                    // Location
                    if (item.lokasiIkan.isNotEmpty)
                      Text(
                        item.lokasiIkan,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ),
            // Action buttons
            if (halaman == 'Wishlist' &&
                (onChatSeller != null || onChatBantuan != null))
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  children: [
                    if (onChatSeller != null)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: onChatSeller,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            minimumSize: const Size(0, 28),
                          ),
                          child: const Text(
                            'Chat',
                            style: TextStyle(fontSize: 10),
                          ),
                        ),
                      ),
                    if (onChatSeller != null && onChatBantuan != null)
                      const SizedBox(width: 4),
                    if (onChatBantuan != null)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: onChatBantuan,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            minimumSize: const Size(0, 28),
                          ),
                          child: const Text(
                            'Help',
                            style: TextStyle(fontSize: 10),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getColorFromString(String colorString) {
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      default:
        return Colors.black;
    }
  }
}
