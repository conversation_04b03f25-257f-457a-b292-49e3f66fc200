import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../../models/ikan_lelang.dart';

/// SellerLayoutView - Single file approach
/// Converted from Swift SellerLayoutView.swift
/// Expandable section showing seller information and social media links
class SellerLayoutView extends StatefulWidget {
  final IkanLelang ikanLelang;

  const SellerLayoutView({super.key, required this.ikanLelang});

  @override
  State<SellerLayoutView> createState() => _SellerLayoutViewState();
}

class _SellerLayoutViewState extends State<SellerLayoutView> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Row(
                children: [
                  const Icon(Icons.person),
                  const SizedBox(width: 8),
                  const Text(
                    'Seller',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  Icon(
                    _isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                  ),
                ],
              ),
            ),

            // Expandable Content
            if (_isExpanded) ...[
              const SizedBox(height: 16),

              // Seller Name
              _buildDetailItem('Nama', widget.ikanLelang.namaMerchantSeller),

              // City
              _buildDetailItem('Kota', widget.ikanLelang.kotaSeller),

              // Level with Icon
              _buildDetailItem(
                'Level',
                null,
                widget: Row(
                  children: [
                    // Level icon placeholder - using a default icon since we don't have the actual icon URL
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.star,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Level ${widget.ikanLelang.ratingSeller.toInt()}', // Using rating as level placeholder
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),

              // Priority Seller (if applicable)
              if (widget.ikanLelang.ratingSeller >=
                  4.5) // Using rating as priority indicator
                _buildDetailItem('Kategori Seller', 'PRIORITY'),

              // Rating
              _buildDetailItem(
                'Rating',
                null,
                widget: Row(
                  children: [
                    RatingBarIndicator(
                      rating: widget.ikanLelang.ratingSeller,
                      itemBuilder:
                          (context, index) =>
                              const Icon(Icons.star, color: Colors.amber),
                      itemCount: 5,
                      itemSize: 24.0,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.ikanLelang.ratingSeller.toStringAsFixed(2),
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '(${widget.ikanLelang.ratingSeller.toInt()}x)', // Using rating as review count placeholder
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),

              // Social Media
              _buildDetailItem(
                'Media Sosial',
                null,
                widget: _buildSocialMediaRow(),
              ),

              // Chat Seller Button
              Padding(
                padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _chatSeller,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/whatsapp.png',
                          width: 20,
                          height: 20,
                          errorBuilder:
                              (context, error, stackTrace) =>
                                  const Icon(Icons.chat, size: 20),
                        ),
                        const SizedBox(width: 8),
                        const Text('Chat Seller !'),
                      ],
                    ),
                  ),
                ),
              ),

              // View Map Location Button
              Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 8),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _viewMapLocation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.map, size: 20),
                        SizedBox(width: 8),
                        Text('Lihat Peta Lokasi'),
                      ],
                    ),
                  ),
                ),
              ),

              // Statistics
              _buildDetailItem(
                'Jumlah Lelang',
                '${widget.ikanLelang.ratingSeller.toInt()} lelang',
              ), // Placeholder
              _buildDetailItem(
                'Jumlah Terlelang',
                '${(widget.ikanLelang.ratingSeller * 10).toInt()} ikan',
              ), // Placeholder
              _buildDetailItem(
                'Rata-rata Terlelang',
                'Rp ${(widget.ikanLelang.ratingSeller * 1000000).toInt()}',
              ), // Placeholder
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String? value, {Widget? widget}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 14)),
          const SizedBox(height: 4),
          if (widget != null)
            Padding(padding: const EdgeInsets.only(left: 16), child: widget)
          else
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(value ?? '-', style: const TextStyle(fontSize: 16)),
            ),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildSocialMediaRow() {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Row(
        children: [
          _buildSocialMediaButton('facebook', 'assets/images/facebook.png', ''),
          const SizedBox(width: 8),
          _buildSocialMediaButton(
            'instagram',
            'assets/images/instagram.png',
            '',
          ),
          const SizedBox(width: 8),
          _buildSocialMediaButton('youtube', 'assets/images/youtube.png', ''),
          const SizedBox(width: 8),
          _buildSocialMediaButton('tiktok', 'assets/images/tiktok.png', ''),
          const SizedBox(width: 8),
          _buildSocialMediaButton('website', 'assets/images/web.png', ''),
        ],
      ),
    );
  }

  Widget _buildSocialMediaButton(
    String platform,
    String assetPath,
    String url,
  ) {
    final bool isActive =
        url.isNotEmpty &&
        (url.startsWith('http://') || url.startsWith('https://'));

    return GestureDetector(
      onTap: () => _onClickSosmed(url),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isActive ? null : Colors.grey[300],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.asset(
            assetPath,
            width: 50,
            height: 50,
            fit: BoxFit.cover,
            color: isActive ? null : Colors.grey,
            colorBlendMode: isActive ? null : BlendMode.saturation,
            errorBuilder:
                (context, error, stackTrace) => Container(
                  width: 50,
                  height: 50,
                  color:
                      isActive
                          ? Theme.of(context).primaryColor
                          : Colors.grey[300],
                  child: Icon(
                    _getSocialMediaIcon(platform),
                    color: isActive ? Colors.white : Colors.grey,
                    size: 24,
                  ),
                ),
          ),
        ),
      ),
    );
  }

  IconData _getSocialMediaIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return Icons.facebook;
      case 'instagram':
        return Icons.camera_alt;
      case 'youtube':
        return Icons.play_circle;
      case 'tiktok':
        return Icons.music_note;
      case 'website':
        return Icons.web;
      default:
        return Icons.link;
    }
  }

  void _chatSeller() async {
    final message =
        'Hai Admin ${widget.ikanLelang.namaMerchantSeller}, '
        'saya mau tanya ikan ini : \n\n'
        '${widget.ikanLelang.namaObyekLelang}\n'
        '${widget.ikanLelang.shortLinkObyekLelang}';

    final url = Uri.parse(
      'https://api.whatsapp.com/send/?phone=${widget.ikanLelang.noHpSeller}'
      '&text=${Uri.encodeComponent(message)}'
      '&type=phone_number&app_absent=0',
    );

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      _showDialogInfo('Informasi', 'WhatsApp tidak terinstall di device ini.');
    }
  }

  void _viewMapLocation() async {
    // Using a placeholder URL since we don't have the actual map URL
    final mapUrl =
        'https://maps.google.com/search/${Uri.encodeComponent(widget.ikanLelang.kotaSeller)}';
    final url = Uri.parse(mapUrl);

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      _showDialogInfo('Informasi', 'Tidak dapat membuka peta.');
    }
  }

  void _onClickSosmed(String sosmedUrl) async {
    if (sosmedUrl.isEmpty ||
        (!sosmedUrl.startsWith('http://') &&
            !sosmedUrl.startsWith('https://'))) {
      _showDialogInfo('Informasi', 'Belum disetting');
      return;
    }

    final url = Uri.parse(sosmedUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      _showDialogInfo('Informasi', 'Tidak dapat membuka link.');
    }
  }

  void _showDialogInfo(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
