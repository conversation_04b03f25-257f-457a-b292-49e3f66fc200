import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/config/flavor_config.dart';
import 'lib/providers/auth_provider.dart';
import 'lib/screens/auth/phone_input_screen.dart';
import 'lib/screens/auth/otp_verification_screen.dart';

void main() {
  runApp(TestAuthApp());
}

class TestAuthApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize flavor configuration for testing
    FlavorConfig(
      flavor: Flavor.koifish,
      name: 'Master Ko<PERSON>',
      appId: 'com.masterkoi.bid',
      appStoreId: '**********',
      appName: 'Master Koi',
      baseUrl: 'https://masterkoi.bid',
      privacyPolicyUrl: 'https://masterkoi.bid/privacy-policy',
      appStoreUrl: 'https://apps.apple.com/id/app/master-koi-bot/id**********',
      logoPath: 'assets/images/logo_koifish.png',
      primaryColor: '#11254E',
      accentColor: '#66BB6A',
    );

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
      ],
      child: MaterialApp(
        title: 'Test Auth Screens',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: TestHomeScreen(),
      ),
    );
  }
}

class TestHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Auth Screens'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PhoneInputScreen(
                      onSuccess: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Login berhasil!')),
                        );
                      },
                    ),
                  ),
                );
              },
              child: Text('Test Phone Input Screen'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OtpVerificationScreen(
                      phoneNumber: '628123456789',
                      onSuccess: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('OTP verification berhasil!')),
                        );
                      },
                    ),
                  ),
                );
              },
              child: Text('Test OTP Verification Screen'),
            ),
          ],
        ),
      ),
    );
  }
}
